import { createSwaggerSpec } from "next-swagger-doc";

export const getApiDocs = async () => {
  const spec = createSwaggerSpec({
    apiFolder: "src/app/api",
    definition: {
      openapi: "3.0.0",
      info: {
        title: "Claims API",
        version: "1.0.0",
        description: "OpenAPI documentation for Claims, Employees, Documents, and Policies API.",
      },
      components: {
        schemas: {
          Employee: {
            type: "object",
            properties: {
              id: { type: "integer" },
              firstName: { type: "string" },
              lastName: { type: "string" },
              email: { type: "string", format: "email" },
              phone: { type: "string", nullable: true },
              address: { type: "string", nullable: true },
              employerName: { type: "string", nullable: true },
              groupId: { type: "string", nullable: true },
              memberId: { type: "string", nullable: true }
            },
            required: ["id", "firstName", "lastName", "email"]
          },
          Claim: {
            type: "object",
            properties: {
              id: { type: "integer" },
              employeeId: { type: "integer" },
              claimType: { type: "string", default: "Critical Illness" },
              description: { type: "string" },
              incidentDate: { type: "string", format: "date-time", nullable: true },
              dateFiled: { type: "string", format: "date-time" },
              status: { type: "string" },
              employee: { $ref: "#/components/schemas/Employee" },
              documents: {
                type: "array",
                items: { $ref: "#/components/schemas/ClaimDocument" }
              },
              comments: {
                type: "array",
                items: { $ref: "#/components/schemas/ClaimComment" }
              }
            },
            required: ["id", "employeeId", "description", "status"]
          },
          ClaimDocument: {
            type: "object",
            properties: {
              id: { type: "integer" },
              claimId: { type: "integer" },
              fileName: { type: "string" },
              filePath: { type: "string" },
              uploadedAt: { type: "string", format: "date-time" }
            },
            required: ["id", "claimId", "fileName", "filePath"]
          },
          ClaimComment: {
            type: "object",
            properties: {
              id: { type: "integer" },
              claimId: { type: "integer" },
              text: { type: "string" },
              createdAt: { type: "string", format: "date-time" }
            },
            required: ["id", "claimId", "text"]
          },
          Policy: {
            type: "object",
            properties: {
              id: { type: "integer" },
              employeeId: { type: "integer" },
              policyOwner: { type: "string" },
              insured: { type: "string" },
              spouse: { type: "string", nullable: true },
              group: { type: "string" },
              policyNumber: { type: "string" },
              originalEffectiveDate: { type: "string", format: "date-time", nullable: true },
              scheduledEffectiveDate: { type: "string", format: "date-time", nullable: true },
              issuedAge: { type: "integer", nullable: true },
              insuredCoverage: { type: "number", format: "float", nullable: true },
              spouseCoverage: { type: "number", format: "float", nullable: true },
              employee: { $ref: "#/components/schemas/Employee" },
              documents: {
                type: "array",
                items: { $ref: "#/components/schemas/PolicyDocument" }
              }
            },
            required: ["id", "employeeId", "policyOwner", "insured", "group", "policyNumber"]
          },
          PolicyDocument: {
            type: "object",
            properties: {
              id: { type: "integer" },
              policyId: { type: "integer" },
              fileName: { type: "string" },
              filePath: { type: "string" },
              uploadedAt: { type: "string", format: "date-time" }
            },
            required: ["id", "policyId", "fileName", "filePath"]
          },
          ClaimStatus: {
            type: "object",
            properties: {
              "Claim ID": { type: "integer" },
              Status: { type: "string" },
              documents: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "string" },
                    createdTime: { type: "string", format: "date-time" },
                    "Document ID": { type: "integer" },
                    Status: { type: "string" },
                    Classification: { type: "string" }
                  }
                }
              },
              notes: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "string" },
                    createdTime: { type: "string", format: "date-time" },
                    "Claim ID": { type: "integer" },
                    Notes: { type: "string" },
                    created_by: { type: "string" },
                    created_at: { type: "string" }
                  }
                }
              }
            },
            required: ["Claim ID", "Status"]
          },
          Error: {
            type: "object",
            properties: {
              error: { type: "string" }
            },
            required: ["error"]
          }
        }
      }
    },
  });
  return spec;
};