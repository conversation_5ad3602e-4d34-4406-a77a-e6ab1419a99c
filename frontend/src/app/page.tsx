import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function Home() {
  return (
    <div className="space-y-8">
      <CardHeader className="px-0">
        <CardTitle className="text-2xl">Claims Portal Dashboard</CardTitle>
      </CardHeader>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6 hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle>Submit a New Claim</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-500">
              Submit a new claim with all required information and supporting documents.
            </p>
            <Link href="/submit-claim">
              <Button>Submit Claim</Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="p-6 hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle>Claims Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-500">
              View, filter, and manage all submitted claims. Update claim status and download documents.
            </p>
            <Link href="/claims-management">
              <Button>Manage Claims</Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 p-6 bg-gray-50 rounded-lg border">
        <h3 className="text-lg font-medium mb-2">Quick Help</h3>
        <ul className="list-disc pl-5 space-y-1">
          <li>Use the <strong>Submit Claim</strong> page to file a new claim with supporting documents</li>
          <li>Use the <strong>Claims Management</strong> page to view and process submitted claims</li>
          <li>All claims require proper documentation to be processed</li>
          <li>For technical support, please contact the IT department</li>
        </ul>
      </div>
    </div>
  );
}
