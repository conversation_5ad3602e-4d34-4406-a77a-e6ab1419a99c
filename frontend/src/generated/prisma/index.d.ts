
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model Employee
 * 
 */
export type Employee = $Result.DefaultSelection<Prisma.$EmployeePayload>
/**
 * Model Claim
 * 
 */
export type Claim = $Result.DefaultSelection<Prisma.$ClaimPayload>
/**
 * Model ClaimDocument
 * 
 */
export type ClaimDocument = $Result.DefaultSelection<Prisma.$ClaimDocumentPayload>
/**
 * Model ClaimComment
 * 
 */
export type ClaimComment = $Result.DefaultSelection<Prisma.$ClaimCommentPayload>
/**
 * Model Policy
 * 
 */
export type Policy = $Result.DefaultSelection<Prisma.$PolicyPayload>
/**
 * Model PolicyDocument
 * 
 */
export type PolicyDocument = $Result.DefaultSelection<Prisma.$PolicyDocumentPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Employees
 * const employees = await prisma.employee.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Employees
   * const employees = await prisma.employee.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.employee`: Exposes CRUD operations for the **Employee** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Employees
    * const employees = await prisma.employee.findMany()
    * ```
    */
  get employee(): Prisma.EmployeeDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.claim`: Exposes CRUD operations for the **Claim** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Claims
    * const claims = await prisma.claim.findMany()
    * ```
    */
  get claim(): Prisma.ClaimDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.claimDocument`: Exposes CRUD operations for the **ClaimDocument** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more ClaimDocuments
    * const claimDocuments = await prisma.claimDocument.findMany()
    * ```
    */
  get claimDocument(): Prisma.ClaimDocumentDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.claimComment`: Exposes CRUD operations for the **ClaimComment** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more ClaimComments
    * const claimComments = await prisma.claimComment.findMany()
    * ```
    */
  get claimComment(): Prisma.ClaimCommentDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.policy`: Exposes CRUD operations for the **Policy** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Policies
    * const policies = await prisma.policy.findMany()
    * ```
    */
  get policy(): Prisma.PolicyDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.policyDocument`: Exposes CRUD operations for the **PolicyDocument** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more PolicyDocuments
    * const policyDocuments = await prisma.policyDocument.findMany()
    * ```
    */
  get policyDocument(): Prisma.PolicyDocumentDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.6.0
   * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    Employee: 'Employee',
    Claim: 'Claim',
    ClaimDocument: 'ClaimDocument',
    ClaimComment: 'ClaimComment',
    Policy: 'Policy',
    PolicyDocument: 'PolicyDocument'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "employee" | "claim" | "claimDocument" | "claimComment" | "policy" | "policyDocument"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      Employee: {
        payload: Prisma.$EmployeePayload<ExtArgs>
        fields: Prisma.EmployeeFieldRefs
        operations: {
          findUnique: {
            args: Prisma.EmployeeFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.EmployeeFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload>
          }
          findFirst: {
            args: Prisma.EmployeeFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.EmployeeFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload>
          }
          findMany: {
            args: Prisma.EmployeeFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload>[]
          }
          create: {
            args: Prisma.EmployeeCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload>
          }
          createMany: {
            args: Prisma.EmployeeCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.EmployeeCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload>[]
          }
          delete: {
            args: Prisma.EmployeeDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload>
          }
          update: {
            args: Prisma.EmployeeUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload>
          }
          deleteMany: {
            args: Prisma.EmployeeDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.EmployeeUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.EmployeeUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload>[]
          }
          upsert: {
            args: Prisma.EmployeeUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EmployeePayload>
          }
          aggregate: {
            args: Prisma.EmployeeAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateEmployee>
          }
          groupBy: {
            args: Prisma.EmployeeGroupByArgs<ExtArgs>
            result: $Utils.Optional<EmployeeGroupByOutputType>[]
          }
          count: {
            args: Prisma.EmployeeCountArgs<ExtArgs>
            result: $Utils.Optional<EmployeeCountAggregateOutputType> | number
          }
        }
      }
      Claim: {
        payload: Prisma.$ClaimPayload<ExtArgs>
        fields: Prisma.ClaimFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ClaimFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ClaimFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload>
          }
          findFirst: {
            args: Prisma.ClaimFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ClaimFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload>
          }
          findMany: {
            args: Prisma.ClaimFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload>[]
          }
          create: {
            args: Prisma.ClaimCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload>
          }
          createMany: {
            args: Prisma.ClaimCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ClaimCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload>[]
          }
          delete: {
            args: Prisma.ClaimDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload>
          }
          update: {
            args: Prisma.ClaimUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload>
          }
          deleteMany: {
            args: Prisma.ClaimDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ClaimUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ClaimUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload>[]
          }
          upsert: {
            args: Prisma.ClaimUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimPayload>
          }
          aggregate: {
            args: Prisma.ClaimAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateClaim>
          }
          groupBy: {
            args: Prisma.ClaimGroupByArgs<ExtArgs>
            result: $Utils.Optional<ClaimGroupByOutputType>[]
          }
          count: {
            args: Prisma.ClaimCountArgs<ExtArgs>
            result: $Utils.Optional<ClaimCountAggregateOutputType> | number
          }
        }
      }
      ClaimDocument: {
        payload: Prisma.$ClaimDocumentPayload<ExtArgs>
        fields: Prisma.ClaimDocumentFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ClaimDocumentFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ClaimDocumentFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload>
          }
          findFirst: {
            args: Prisma.ClaimDocumentFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ClaimDocumentFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload>
          }
          findMany: {
            args: Prisma.ClaimDocumentFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload>[]
          }
          create: {
            args: Prisma.ClaimDocumentCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload>
          }
          createMany: {
            args: Prisma.ClaimDocumentCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ClaimDocumentCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload>[]
          }
          delete: {
            args: Prisma.ClaimDocumentDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload>
          }
          update: {
            args: Prisma.ClaimDocumentUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload>
          }
          deleteMany: {
            args: Prisma.ClaimDocumentDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ClaimDocumentUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ClaimDocumentUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload>[]
          }
          upsert: {
            args: Prisma.ClaimDocumentUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimDocumentPayload>
          }
          aggregate: {
            args: Prisma.ClaimDocumentAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateClaimDocument>
          }
          groupBy: {
            args: Prisma.ClaimDocumentGroupByArgs<ExtArgs>
            result: $Utils.Optional<ClaimDocumentGroupByOutputType>[]
          }
          count: {
            args: Prisma.ClaimDocumentCountArgs<ExtArgs>
            result: $Utils.Optional<ClaimDocumentCountAggregateOutputType> | number
          }
        }
      }
      ClaimComment: {
        payload: Prisma.$ClaimCommentPayload<ExtArgs>
        fields: Prisma.ClaimCommentFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ClaimCommentFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ClaimCommentFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload>
          }
          findFirst: {
            args: Prisma.ClaimCommentFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ClaimCommentFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload>
          }
          findMany: {
            args: Prisma.ClaimCommentFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload>[]
          }
          create: {
            args: Prisma.ClaimCommentCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload>
          }
          createMany: {
            args: Prisma.ClaimCommentCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ClaimCommentCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload>[]
          }
          delete: {
            args: Prisma.ClaimCommentDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload>
          }
          update: {
            args: Prisma.ClaimCommentUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload>
          }
          deleteMany: {
            args: Prisma.ClaimCommentDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ClaimCommentUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ClaimCommentUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload>[]
          }
          upsert: {
            args: Prisma.ClaimCommentUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClaimCommentPayload>
          }
          aggregate: {
            args: Prisma.ClaimCommentAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateClaimComment>
          }
          groupBy: {
            args: Prisma.ClaimCommentGroupByArgs<ExtArgs>
            result: $Utils.Optional<ClaimCommentGroupByOutputType>[]
          }
          count: {
            args: Prisma.ClaimCommentCountArgs<ExtArgs>
            result: $Utils.Optional<ClaimCommentCountAggregateOutputType> | number
          }
        }
      }
      Policy: {
        payload: Prisma.$PolicyPayload<ExtArgs>
        fields: Prisma.PolicyFieldRefs
        operations: {
          findUnique: {
            args: Prisma.PolicyFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.PolicyFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload>
          }
          findFirst: {
            args: Prisma.PolicyFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.PolicyFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload>
          }
          findMany: {
            args: Prisma.PolicyFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload>[]
          }
          create: {
            args: Prisma.PolicyCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload>
          }
          createMany: {
            args: Prisma.PolicyCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.PolicyCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload>[]
          }
          delete: {
            args: Prisma.PolicyDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload>
          }
          update: {
            args: Prisma.PolicyUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload>
          }
          deleteMany: {
            args: Prisma.PolicyDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.PolicyUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.PolicyUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload>[]
          }
          upsert: {
            args: Prisma.PolicyUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyPayload>
          }
          aggregate: {
            args: Prisma.PolicyAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregatePolicy>
          }
          groupBy: {
            args: Prisma.PolicyGroupByArgs<ExtArgs>
            result: $Utils.Optional<PolicyGroupByOutputType>[]
          }
          count: {
            args: Prisma.PolicyCountArgs<ExtArgs>
            result: $Utils.Optional<PolicyCountAggregateOutputType> | number
          }
        }
      }
      PolicyDocument: {
        payload: Prisma.$PolicyDocumentPayload<ExtArgs>
        fields: Prisma.PolicyDocumentFieldRefs
        operations: {
          findUnique: {
            args: Prisma.PolicyDocumentFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.PolicyDocumentFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload>
          }
          findFirst: {
            args: Prisma.PolicyDocumentFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.PolicyDocumentFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload>
          }
          findMany: {
            args: Prisma.PolicyDocumentFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload>[]
          }
          create: {
            args: Prisma.PolicyDocumentCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload>
          }
          createMany: {
            args: Prisma.PolicyDocumentCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.PolicyDocumentCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload>[]
          }
          delete: {
            args: Prisma.PolicyDocumentDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload>
          }
          update: {
            args: Prisma.PolicyDocumentUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload>
          }
          deleteMany: {
            args: Prisma.PolicyDocumentDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.PolicyDocumentUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.PolicyDocumentUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload>[]
          }
          upsert: {
            args: Prisma.PolicyDocumentUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PolicyDocumentPayload>
          }
          aggregate: {
            args: Prisma.PolicyDocumentAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregatePolicyDocument>
          }
          groupBy: {
            args: Prisma.PolicyDocumentGroupByArgs<ExtArgs>
            result: $Utils.Optional<PolicyDocumentGroupByOutputType>[]
          }
          count: {
            args: Prisma.PolicyDocumentCountArgs<ExtArgs>
            result: $Utils.Optional<PolicyDocumentCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    employee?: EmployeeOmit
    claim?: ClaimOmit
    claimDocument?: ClaimDocumentOmit
    claimComment?: ClaimCommentOmit
    policy?: PolicyOmit
    policyDocument?: PolicyDocumentOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type EmployeeCountOutputType
   */

  export type EmployeeCountOutputType = {
    claims: number
  }

  export type EmployeeCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    claims?: boolean | EmployeeCountOutputTypeCountClaimsArgs
  }

  // Custom InputTypes
  /**
   * EmployeeCountOutputType without action
   */
  export type EmployeeCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EmployeeCountOutputType
     */
    select?: EmployeeCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * EmployeeCountOutputType without action
   */
  export type EmployeeCountOutputTypeCountClaimsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ClaimWhereInput
  }


  /**
   * Count Type ClaimCountOutputType
   */

  export type ClaimCountOutputType = {
    documents: number
    comments: number
  }

  export type ClaimCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    documents?: boolean | ClaimCountOutputTypeCountDocumentsArgs
    comments?: boolean | ClaimCountOutputTypeCountCommentsArgs
  }

  // Custom InputTypes
  /**
   * ClaimCountOutputType without action
   */
  export type ClaimCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimCountOutputType
     */
    select?: ClaimCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * ClaimCountOutputType without action
   */
  export type ClaimCountOutputTypeCountDocumentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ClaimDocumentWhereInput
  }

  /**
   * ClaimCountOutputType without action
   */
  export type ClaimCountOutputTypeCountCommentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ClaimCommentWhereInput
  }


  /**
   * Count Type PolicyCountOutputType
   */

  export type PolicyCountOutputType = {
    documents: number
  }

  export type PolicyCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    documents?: boolean | PolicyCountOutputTypeCountDocumentsArgs
  }

  // Custom InputTypes
  /**
   * PolicyCountOutputType without action
   */
  export type PolicyCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyCountOutputType
     */
    select?: PolicyCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * PolicyCountOutputType without action
   */
  export type PolicyCountOutputTypeCountDocumentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PolicyDocumentWhereInput
  }


  /**
   * Models
   */

  /**
   * Model Employee
   */

  export type AggregateEmployee = {
    _count: EmployeeCountAggregateOutputType | null
    _avg: EmployeeAvgAggregateOutputType | null
    _sum: EmployeeSumAggregateOutputType | null
    _min: EmployeeMinAggregateOutputType | null
    _max: EmployeeMaxAggregateOutputType | null
  }

  export type EmployeeAvgAggregateOutputType = {
    id: number | null
  }

  export type EmployeeSumAggregateOutputType = {
    id: number | null
  }

  export type EmployeeMinAggregateOutputType = {
    id: number | null
    firstName: string | null
    lastName: string | null
    email: string | null
    phone: string | null
    address: string | null
    employerName: string | null
    groupId: string | null
    memberId: string | null
  }

  export type EmployeeMaxAggregateOutputType = {
    id: number | null
    firstName: string | null
    lastName: string | null
    email: string | null
    phone: string | null
    address: string | null
    employerName: string | null
    groupId: string | null
    memberId: string | null
  }

  export type EmployeeCountAggregateOutputType = {
    id: number
    firstName: number
    lastName: number
    email: number
    phone: number
    address: number
    employerName: number
    groupId: number
    memberId: number
    _all: number
  }


  export type EmployeeAvgAggregateInputType = {
    id?: true
  }

  export type EmployeeSumAggregateInputType = {
    id?: true
  }

  export type EmployeeMinAggregateInputType = {
    id?: true
    firstName?: true
    lastName?: true
    email?: true
    phone?: true
    address?: true
    employerName?: true
    groupId?: true
    memberId?: true
  }

  export type EmployeeMaxAggregateInputType = {
    id?: true
    firstName?: true
    lastName?: true
    email?: true
    phone?: true
    address?: true
    employerName?: true
    groupId?: true
    memberId?: true
  }

  export type EmployeeCountAggregateInputType = {
    id?: true
    firstName?: true
    lastName?: true
    email?: true
    phone?: true
    address?: true
    employerName?: true
    groupId?: true
    memberId?: true
    _all?: true
  }

  export type EmployeeAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Employee to aggregate.
     */
    where?: EmployeeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Employees to fetch.
     */
    orderBy?: EmployeeOrderByWithRelationInput | EmployeeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: EmployeeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Employees from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Employees.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Employees
    **/
    _count?: true | EmployeeCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: EmployeeAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: EmployeeSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: EmployeeMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: EmployeeMaxAggregateInputType
  }

  export type GetEmployeeAggregateType<T extends EmployeeAggregateArgs> = {
        [P in keyof T & keyof AggregateEmployee]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateEmployee[P]>
      : GetScalarType<T[P], AggregateEmployee[P]>
  }




  export type EmployeeGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: EmployeeWhereInput
    orderBy?: EmployeeOrderByWithAggregationInput | EmployeeOrderByWithAggregationInput[]
    by: EmployeeScalarFieldEnum[] | EmployeeScalarFieldEnum
    having?: EmployeeScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: EmployeeCountAggregateInputType | true
    _avg?: EmployeeAvgAggregateInputType
    _sum?: EmployeeSumAggregateInputType
    _min?: EmployeeMinAggregateInputType
    _max?: EmployeeMaxAggregateInputType
  }

  export type EmployeeGroupByOutputType = {
    id: number
    firstName: string
    lastName: string
    email: string
    phone: string | null
    address: string | null
    employerName: string | null
    groupId: string | null
    memberId: string | null
    _count: EmployeeCountAggregateOutputType | null
    _avg: EmployeeAvgAggregateOutputType | null
    _sum: EmployeeSumAggregateOutputType | null
    _min: EmployeeMinAggregateOutputType | null
    _max: EmployeeMaxAggregateOutputType | null
  }

  type GetEmployeeGroupByPayload<T extends EmployeeGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<EmployeeGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof EmployeeGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], EmployeeGroupByOutputType[P]>
            : GetScalarType<T[P], EmployeeGroupByOutputType[P]>
        }
      >
    >


  export type EmployeeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    firstName?: boolean
    lastName?: boolean
    email?: boolean
    phone?: boolean
    address?: boolean
    employerName?: boolean
    groupId?: boolean
    memberId?: boolean
    claims?: boolean | Employee$claimsArgs<ExtArgs>
    policy?: boolean | Employee$policyArgs<ExtArgs>
    _count?: boolean | EmployeeCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["employee"]>

  export type EmployeeSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    firstName?: boolean
    lastName?: boolean
    email?: boolean
    phone?: boolean
    address?: boolean
    employerName?: boolean
    groupId?: boolean
    memberId?: boolean
  }, ExtArgs["result"]["employee"]>

  export type EmployeeSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    firstName?: boolean
    lastName?: boolean
    email?: boolean
    phone?: boolean
    address?: boolean
    employerName?: boolean
    groupId?: boolean
    memberId?: boolean
  }, ExtArgs["result"]["employee"]>

  export type EmployeeSelectScalar = {
    id?: boolean
    firstName?: boolean
    lastName?: boolean
    email?: boolean
    phone?: boolean
    address?: boolean
    employerName?: boolean
    groupId?: boolean
    memberId?: boolean
  }

  export type EmployeeOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "firstName" | "lastName" | "email" | "phone" | "address" | "employerName" | "groupId" | "memberId", ExtArgs["result"]["employee"]>
  export type EmployeeInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    claims?: boolean | Employee$claimsArgs<ExtArgs>
    policy?: boolean | Employee$policyArgs<ExtArgs>
    _count?: boolean | EmployeeCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type EmployeeIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type EmployeeIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $EmployeePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Employee"
    objects: {
      claims: Prisma.$ClaimPayload<ExtArgs>[]
      policy: Prisma.$PolicyPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      firstName: string
      lastName: string
      email: string
      phone: string | null
      address: string | null
      employerName: string | null
      groupId: string | null
      memberId: string | null
    }, ExtArgs["result"]["employee"]>
    composites: {}
  }

  type EmployeeGetPayload<S extends boolean | null | undefined | EmployeeDefaultArgs> = $Result.GetResult<Prisma.$EmployeePayload, S>

  type EmployeeCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<EmployeeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: EmployeeCountAggregateInputType | true
    }

  export interface EmployeeDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Employee'], meta: { name: 'Employee' } }
    /**
     * Find zero or one Employee that matches the filter.
     * @param {EmployeeFindUniqueArgs} args - Arguments to find a Employee
     * @example
     * // Get one Employee
     * const employee = await prisma.employee.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends EmployeeFindUniqueArgs>(args: SelectSubset<T, EmployeeFindUniqueArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Employee that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {EmployeeFindUniqueOrThrowArgs} args - Arguments to find a Employee
     * @example
     * // Get one Employee
     * const employee = await prisma.employee.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends EmployeeFindUniqueOrThrowArgs>(args: SelectSubset<T, EmployeeFindUniqueOrThrowArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Employee that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EmployeeFindFirstArgs} args - Arguments to find a Employee
     * @example
     * // Get one Employee
     * const employee = await prisma.employee.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends EmployeeFindFirstArgs>(args?: SelectSubset<T, EmployeeFindFirstArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Employee that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EmployeeFindFirstOrThrowArgs} args - Arguments to find a Employee
     * @example
     * // Get one Employee
     * const employee = await prisma.employee.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends EmployeeFindFirstOrThrowArgs>(args?: SelectSubset<T, EmployeeFindFirstOrThrowArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Employees that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EmployeeFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Employees
     * const employees = await prisma.employee.findMany()
     * 
     * // Get first 10 Employees
     * const employees = await prisma.employee.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const employeeWithIdOnly = await prisma.employee.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends EmployeeFindManyArgs>(args?: SelectSubset<T, EmployeeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Employee.
     * @param {EmployeeCreateArgs} args - Arguments to create a Employee.
     * @example
     * // Create one Employee
     * const Employee = await prisma.employee.create({
     *   data: {
     *     // ... data to create a Employee
     *   }
     * })
     * 
     */
    create<T extends EmployeeCreateArgs>(args: SelectSubset<T, EmployeeCreateArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Employees.
     * @param {EmployeeCreateManyArgs} args - Arguments to create many Employees.
     * @example
     * // Create many Employees
     * const employee = await prisma.employee.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends EmployeeCreateManyArgs>(args?: SelectSubset<T, EmployeeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Employees and returns the data saved in the database.
     * @param {EmployeeCreateManyAndReturnArgs} args - Arguments to create many Employees.
     * @example
     * // Create many Employees
     * const employee = await prisma.employee.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Employees and only return the `id`
     * const employeeWithIdOnly = await prisma.employee.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends EmployeeCreateManyAndReturnArgs>(args?: SelectSubset<T, EmployeeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Employee.
     * @param {EmployeeDeleteArgs} args - Arguments to delete one Employee.
     * @example
     * // Delete one Employee
     * const Employee = await prisma.employee.delete({
     *   where: {
     *     // ... filter to delete one Employee
     *   }
     * })
     * 
     */
    delete<T extends EmployeeDeleteArgs>(args: SelectSubset<T, EmployeeDeleteArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Employee.
     * @param {EmployeeUpdateArgs} args - Arguments to update one Employee.
     * @example
     * // Update one Employee
     * const employee = await prisma.employee.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends EmployeeUpdateArgs>(args: SelectSubset<T, EmployeeUpdateArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Employees.
     * @param {EmployeeDeleteManyArgs} args - Arguments to filter Employees to delete.
     * @example
     * // Delete a few Employees
     * const { count } = await prisma.employee.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends EmployeeDeleteManyArgs>(args?: SelectSubset<T, EmployeeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Employees.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EmployeeUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Employees
     * const employee = await prisma.employee.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends EmployeeUpdateManyArgs>(args: SelectSubset<T, EmployeeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Employees and returns the data updated in the database.
     * @param {EmployeeUpdateManyAndReturnArgs} args - Arguments to update many Employees.
     * @example
     * // Update many Employees
     * const employee = await prisma.employee.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Employees and only return the `id`
     * const employeeWithIdOnly = await prisma.employee.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends EmployeeUpdateManyAndReturnArgs>(args: SelectSubset<T, EmployeeUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Employee.
     * @param {EmployeeUpsertArgs} args - Arguments to update or create a Employee.
     * @example
     * // Update or create a Employee
     * const employee = await prisma.employee.upsert({
     *   create: {
     *     // ... data to create a Employee
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Employee we want to update
     *   }
     * })
     */
    upsert<T extends EmployeeUpsertArgs>(args: SelectSubset<T, EmployeeUpsertArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Employees.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EmployeeCountArgs} args - Arguments to filter Employees to count.
     * @example
     * // Count the number of Employees
     * const count = await prisma.employee.count({
     *   where: {
     *     // ... the filter for the Employees we want to count
     *   }
     * })
    **/
    count<T extends EmployeeCountArgs>(
      args?: Subset<T, EmployeeCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], EmployeeCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Employee.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EmployeeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends EmployeeAggregateArgs>(args: Subset<T, EmployeeAggregateArgs>): Prisma.PrismaPromise<GetEmployeeAggregateType<T>>

    /**
     * Group by Employee.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EmployeeGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends EmployeeGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: EmployeeGroupByArgs['orderBy'] }
        : { orderBy?: EmployeeGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, EmployeeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetEmployeeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Employee model
   */
  readonly fields: EmployeeFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Employee.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__EmployeeClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    claims<T extends Employee$claimsArgs<ExtArgs> = {}>(args?: Subset<T, Employee$claimsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    policy<T extends Employee$policyArgs<ExtArgs> = {}>(args?: Subset<T, Employee$policyArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Employee model
   */
  interface EmployeeFieldRefs {
    readonly id: FieldRef<"Employee", 'Int'>
    readonly firstName: FieldRef<"Employee", 'String'>
    readonly lastName: FieldRef<"Employee", 'String'>
    readonly email: FieldRef<"Employee", 'String'>
    readonly phone: FieldRef<"Employee", 'String'>
    readonly address: FieldRef<"Employee", 'String'>
    readonly employerName: FieldRef<"Employee", 'String'>
    readonly groupId: FieldRef<"Employee", 'String'>
    readonly memberId: FieldRef<"Employee", 'String'>
  }
    

  // Custom InputTypes
  /**
   * Employee findUnique
   */
  export type EmployeeFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
    /**
     * Filter, which Employee to fetch.
     */
    where: EmployeeWhereUniqueInput
  }

  /**
   * Employee findUniqueOrThrow
   */
  export type EmployeeFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
    /**
     * Filter, which Employee to fetch.
     */
    where: EmployeeWhereUniqueInput
  }

  /**
   * Employee findFirst
   */
  export type EmployeeFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
    /**
     * Filter, which Employee to fetch.
     */
    where?: EmployeeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Employees to fetch.
     */
    orderBy?: EmployeeOrderByWithRelationInput | EmployeeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Employees.
     */
    cursor?: EmployeeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Employees from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Employees.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Employees.
     */
    distinct?: EmployeeScalarFieldEnum | EmployeeScalarFieldEnum[]
  }

  /**
   * Employee findFirstOrThrow
   */
  export type EmployeeFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
    /**
     * Filter, which Employee to fetch.
     */
    where?: EmployeeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Employees to fetch.
     */
    orderBy?: EmployeeOrderByWithRelationInput | EmployeeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Employees.
     */
    cursor?: EmployeeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Employees from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Employees.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Employees.
     */
    distinct?: EmployeeScalarFieldEnum | EmployeeScalarFieldEnum[]
  }

  /**
   * Employee findMany
   */
  export type EmployeeFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
    /**
     * Filter, which Employees to fetch.
     */
    where?: EmployeeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Employees to fetch.
     */
    orderBy?: EmployeeOrderByWithRelationInput | EmployeeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Employees.
     */
    cursor?: EmployeeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Employees from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Employees.
     */
    skip?: number
    distinct?: EmployeeScalarFieldEnum | EmployeeScalarFieldEnum[]
  }

  /**
   * Employee create
   */
  export type EmployeeCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
    /**
     * The data needed to create a Employee.
     */
    data: XOR<EmployeeCreateInput, EmployeeUncheckedCreateInput>
  }

  /**
   * Employee createMany
   */
  export type EmployeeCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Employees.
     */
    data: EmployeeCreateManyInput | EmployeeCreateManyInput[]
  }

  /**
   * Employee createManyAndReturn
   */
  export type EmployeeCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * The data used to create many Employees.
     */
    data: EmployeeCreateManyInput | EmployeeCreateManyInput[]
  }

  /**
   * Employee update
   */
  export type EmployeeUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
    /**
     * The data needed to update a Employee.
     */
    data: XOR<EmployeeUpdateInput, EmployeeUncheckedUpdateInput>
    /**
     * Choose, which Employee to update.
     */
    where: EmployeeWhereUniqueInput
  }

  /**
   * Employee updateMany
   */
  export type EmployeeUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Employees.
     */
    data: XOR<EmployeeUpdateManyMutationInput, EmployeeUncheckedUpdateManyInput>
    /**
     * Filter which Employees to update
     */
    where?: EmployeeWhereInput
    /**
     * Limit how many Employees to update.
     */
    limit?: number
  }

  /**
   * Employee updateManyAndReturn
   */
  export type EmployeeUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * The data used to update Employees.
     */
    data: XOR<EmployeeUpdateManyMutationInput, EmployeeUncheckedUpdateManyInput>
    /**
     * Filter which Employees to update
     */
    where?: EmployeeWhereInput
    /**
     * Limit how many Employees to update.
     */
    limit?: number
  }

  /**
   * Employee upsert
   */
  export type EmployeeUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
    /**
     * The filter to search for the Employee to update in case it exists.
     */
    where: EmployeeWhereUniqueInput
    /**
     * In case the Employee found by the `where` argument doesn't exist, create a new Employee with this data.
     */
    create: XOR<EmployeeCreateInput, EmployeeUncheckedCreateInput>
    /**
     * In case the Employee was found with the provided `where` argument, update it with this data.
     */
    update: XOR<EmployeeUpdateInput, EmployeeUncheckedUpdateInput>
  }

  /**
   * Employee delete
   */
  export type EmployeeDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
    /**
     * Filter which Employee to delete.
     */
    where: EmployeeWhereUniqueInput
  }

  /**
   * Employee deleteMany
   */
  export type EmployeeDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Employees to delete
     */
    where?: EmployeeWhereInput
    /**
     * Limit how many Employees to delete.
     */
    limit?: number
  }

  /**
   * Employee.claims
   */
  export type Employee$claimsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    where?: ClaimWhereInput
    orderBy?: ClaimOrderByWithRelationInput | ClaimOrderByWithRelationInput[]
    cursor?: ClaimWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ClaimScalarFieldEnum | ClaimScalarFieldEnum[]
  }

  /**
   * Employee.policy
   */
  export type Employee$policyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    where?: PolicyWhereInput
  }

  /**
   * Employee without action
   */
  export type EmployeeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Employee
     */
    select?: EmployeeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Employee
     */
    omit?: EmployeeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EmployeeInclude<ExtArgs> | null
  }


  /**
   * Model Claim
   */

  export type AggregateClaim = {
    _count: ClaimCountAggregateOutputType | null
    _avg: ClaimAvgAggregateOutputType | null
    _sum: ClaimSumAggregateOutputType | null
    _min: ClaimMinAggregateOutputType | null
    _max: ClaimMaxAggregateOutputType | null
  }

  export type ClaimAvgAggregateOutputType = {
    id: number | null
    employeeId: number | null
  }

  export type ClaimSumAggregateOutputType = {
    id: number | null
    employeeId: number | null
  }

  export type ClaimMinAggregateOutputType = {
    id: number | null
    employeeId: number | null
    claimType: string | null
    description: string | null
    incidentDate: Date | null
    dateFiled: Date | null
    status: string | null
  }

  export type ClaimMaxAggregateOutputType = {
    id: number | null
    employeeId: number | null
    claimType: string | null
    description: string | null
    incidentDate: Date | null
    dateFiled: Date | null
    status: string | null
  }

  export type ClaimCountAggregateOutputType = {
    id: number
    employeeId: number
    claimType: number
    description: number
    incidentDate: number
    dateFiled: number
    status: number
    _all: number
  }


  export type ClaimAvgAggregateInputType = {
    id?: true
    employeeId?: true
  }

  export type ClaimSumAggregateInputType = {
    id?: true
    employeeId?: true
  }

  export type ClaimMinAggregateInputType = {
    id?: true
    employeeId?: true
    claimType?: true
    description?: true
    incidentDate?: true
    dateFiled?: true
    status?: true
  }

  export type ClaimMaxAggregateInputType = {
    id?: true
    employeeId?: true
    claimType?: true
    description?: true
    incidentDate?: true
    dateFiled?: true
    status?: true
  }

  export type ClaimCountAggregateInputType = {
    id?: true
    employeeId?: true
    claimType?: true
    description?: true
    incidentDate?: true
    dateFiled?: true
    status?: true
    _all?: true
  }

  export type ClaimAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Claim to aggregate.
     */
    where?: ClaimWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Claims to fetch.
     */
    orderBy?: ClaimOrderByWithRelationInput | ClaimOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ClaimWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Claims from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Claims.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Claims
    **/
    _count?: true | ClaimCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: ClaimAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: ClaimSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ClaimMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ClaimMaxAggregateInputType
  }

  export type GetClaimAggregateType<T extends ClaimAggregateArgs> = {
        [P in keyof T & keyof AggregateClaim]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateClaim[P]>
      : GetScalarType<T[P], AggregateClaim[P]>
  }




  export type ClaimGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ClaimWhereInput
    orderBy?: ClaimOrderByWithAggregationInput | ClaimOrderByWithAggregationInput[]
    by: ClaimScalarFieldEnum[] | ClaimScalarFieldEnum
    having?: ClaimScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ClaimCountAggregateInputType | true
    _avg?: ClaimAvgAggregateInputType
    _sum?: ClaimSumAggregateInputType
    _min?: ClaimMinAggregateInputType
    _max?: ClaimMaxAggregateInputType
  }

  export type ClaimGroupByOutputType = {
    id: number
    employeeId: number
    claimType: string
    description: string
    incidentDate: Date | null
    dateFiled: Date
    status: string
    _count: ClaimCountAggregateOutputType | null
    _avg: ClaimAvgAggregateOutputType | null
    _sum: ClaimSumAggregateOutputType | null
    _min: ClaimMinAggregateOutputType | null
    _max: ClaimMaxAggregateOutputType | null
  }

  type GetClaimGroupByPayload<T extends ClaimGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ClaimGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ClaimGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ClaimGroupByOutputType[P]>
            : GetScalarType<T[P], ClaimGroupByOutputType[P]>
        }
      >
    >


  export type ClaimSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    employeeId?: boolean
    claimType?: boolean
    description?: boolean
    incidentDate?: boolean
    dateFiled?: boolean
    status?: boolean
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
    documents?: boolean | Claim$documentsArgs<ExtArgs>
    comments?: boolean | Claim$commentsArgs<ExtArgs>
    _count?: boolean | ClaimCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["claim"]>

  export type ClaimSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    employeeId?: boolean
    claimType?: boolean
    description?: boolean
    incidentDate?: boolean
    dateFiled?: boolean
    status?: boolean
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["claim"]>

  export type ClaimSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    employeeId?: boolean
    claimType?: boolean
    description?: boolean
    incidentDate?: boolean
    dateFiled?: boolean
    status?: boolean
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["claim"]>

  export type ClaimSelectScalar = {
    id?: boolean
    employeeId?: boolean
    claimType?: boolean
    description?: boolean
    incidentDate?: boolean
    dateFiled?: boolean
    status?: boolean
  }

  export type ClaimOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "employeeId" | "claimType" | "description" | "incidentDate" | "dateFiled" | "status", ExtArgs["result"]["claim"]>
  export type ClaimInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
    documents?: boolean | Claim$documentsArgs<ExtArgs>
    comments?: boolean | Claim$commentsArgs<ExtArgs>
    _count?: boolean | ClaimCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type ClaimIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
  }
  export type ClaimIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
  }

  export type $ClaimPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Claim"
    objects: {
      employee: Prisma.$EmployeePayload<ExtArgs>
      documents: Prisma.$ClaimDocumentPayload<ExtArgs>[]
      comments: Prisma.$ClaimCommentPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      employeeId: number
      claimType: string
      description: string
      incidentDate: Date | null
      dateFiled: Date
      status: string
    }, ExtArgs["result"]["claim"]>
    composites: {}
  }

  type ClaimGetPayload<S extends boolean | null | undefined | ClaimDefaultArgs> = $Result.GetResult<Prisma.$ClaimPayload, S>

  type ClaimCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ClaimFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ClaimCountAggregateInputType | true
    }

  export interface ClaimDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Claim'], meta: { name: 'Claim' } }
    /**
     * Find zero or one Claim that matches the filter.
     * @param {ClaimFindUniqueArgs} args - Arguments to find a Claim
     * @example
     * // Get one Claim
     * const claim = await prisma.claim.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ClaimFindUniqueArgs>(args: SelectSubset<T, ClaimFindUniqueArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Claim that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ClaimFindUniqueOrThrowArgs} args - Arguments to find a Claim
     * @example
     * // Get one Claim
     * const claim = await prisma.claim.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ClaimFindUniqueOrThrowArgs>(args: SelectSubset<T, ClaimFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Claim that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimFindFirstArgs} args - Arguments to find a Claim
     * @example
     * // Get one Claim
     * const claim = await prisma.claim.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ClaimFindFirstArgs>(args?: SelectSubset<T, ClaimFindFirstArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Claim that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimFindFirstOrThrowArgs} args - Arguments to find a Claim
     * @example
     * // Get one Claim
     * const claim = await prisma.claim.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ClaimFindFirstOrThrowArgs>(args?: SelectSubset<T, ClaimFindFirstOrThrowArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Claims that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Claims
     * const claims = await prisma.claim.findMany()
     * 
     * // Get first 10 Claims
     * const claims = await prisma.claim.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const claimWithIdOnly = await prisma.claim.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ClaimFindManyArgs>(args?: SelectSubset<T, ClaimFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Claim.
     * @param {ClaimCreateArgs} args - Arguments to create a Claim.
     * @example
     * // Create one Claim
     * const Claim = await prisma.claim.create({
     *   data: {
     *     // ... data to create a Claim
     *   }
     * })
     * 
     */
    create<T extends ClaimCreateArgs>(args: SelectSubset<T, ClaimCreateArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Claims.
     * @param {ClaimCreateManyArgs} args - Arguments to create many Claims.
     * @example
     * // Create many Claims
     * const claim = await prisma.claim.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ClaimCreateManyArgs>(args?: SelectSubset<T, ClaimCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Claims and returns the data saved in the database.
     * @param {ClaimCreateManyAndReturnArgs} args - Arguments to create many Claims.
     * @example
     * // Create many Claims
     * const claim = await prisma.claim.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Claims and only return the `id`
     * const claimWithIdOnly = await prisma.claim.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ClaimCreateManyAndReturnArgs>(args?: SelectSubset<T, ClaimCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Claim.
     * @param {ClaimDeleteArgs} args - Arguments to delete one Claim.
     * @example
     * // Delete one Claim
     * const Claim = await prisma.claim.delete({
     *   where: {
     *     // ... filter to delete one Claim
     *   }
     * })
     * 
     */
    delete<T extends ClaimDeleteArgs>(args: SelectSubset<T, ClaimDeleteArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Claim.
     * @param {ClaimUpdateArgs} args - Arguments to update one Claim.
     * @example
     * // Update one Claim
     * const claim = await prisma.claim.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ClaimUpdateArgs>(args: SelectSubset<T, ClaimUpdateArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Claims.
     * @param {ClaimDeleteManyArgs} args - Arguments to filter Claims to delete.
     * @example
     * // Delete a few Claims
     * const { count } = await prisma.claim.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ClaimDeleteManyArgs>(args?: SelectSubset<T, ClaimDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Claims.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Claims
     * const claim = await prisma.claim.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ClaimUpdateManyArgs>(args: SelectSubset<T, ClaimUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Claims and returns the data updated in the database.
     * @param {ClaimUpdateManyAndReturnArgs} args - Arguments to update many Claims.
     * @example
     * // Update many Claims
     * const claim = await prisma.claim.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Claims and only return the `id`
     * const claimWithIdOnly = await prisma.claim.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ClaimUpdateManyAndReturnArgs>(args: SelectSubset<T, ClaimUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Claim.
     * @param {ClaimUpsertArgs} args - Arguments to update or create a Claim.
     * @example
     * // Update or create a Claim
     * const claim = await prisma.claim.upsert({
     *   create: {
     *     // ... data to create a Claim
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Claim we want to update
     *   }
     * })
     */
    upsert<T extends ClaimUpsertArgs>(args: SelectSubset<T, ClaimUpsertArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Claims.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimCountArgs} args - Arguments to filter Claims to count.
     * @example
     * // Count the number of Claims
     * const count = await prisma.claim.count({
     *   where: {
     *     // ... the filter for the Claims we want to count
     *   }
     * })
    **/
    count<T extends ClaimCountArgs>(
      args?: Subset<T, ClaimCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ClaimCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Claim.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ClaimAggregateArgs>(args: Subset<T, ClaimAggregateArgs>): Prisma.PrismaPromise<GetClaimAggregateType<T>>

    /**
     * Group by Claim.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ClaimGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ClaimGroupByArgs['orderBy'] }
        : { orderBy?: ClaimGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ClaimGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetClaimGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Claim model
   */
  readonly fields: ClaimFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Claim.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ClaimClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    employee<T extends EmployeeDefaultArgs<ExtArgs> = {}>(args?: Subset<T, EmployeeDefaultArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    documents<T extends Claim$documentsArgs<ExtArgs> = {}>(args?: Subset<T, Claim$documentsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    comments<T extends Claim$commentsArgs<ExtArgs> = {}>(args?: Subset<T, Claim$commentsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Claim model
   */
  interface ClaimFieldRefs {
    readonly id: FieldRef<"Claim", 'Int'>
    readonly employeeId: FieldRef<"Claim", 'Int'>
    readonly claimType: FieldRef<"Claim", 'String'>
    readonly description: FieldRef<"Claim", 'String'>
    readonly incidentDate: FieldRef<"Claim", 'DateTime'>
    readonly dateFiled: FieldRef<"Claim", 'DateTime'>
    readonly status: FieldRef<"Claim", 'String'>
  }
    

  // Custom InputTypes
  /**
   * Claim findUnique
   */
  export type ClaimFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    /**
     * Filter, which Claim to fetch.
     */
    where: ClaimWhereUniqueInput
  }

  /**
   * Claim findUniqueOrThrow
   */
  export type ClaimFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    /**
     * Filter, which Claim to fetch.
     */
    where: ClaimWhereUniqueInput
  }

  /**
   * Claim findFirst
   */
  export type ClaimFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    /**
     * Filter, which Claim to fetch.
     */
    where?: ClaimWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Claims to fetch.
     */
    orderBy?: ClaimOrderByWithRelationInput | ClaimOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Claims.
     */
    cursor?: ClaimWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Claims from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Claims.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Claims.
     */
    distinct?: ClaimScalarFieldEnum | ClaimScalarFieldEnum[]
  }

  /**
   * Claim findFirstOrThrow
   */
  export type ClaimFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    /**
     * Filter, which Claim to fetch.
     */
    where?: ClaimWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Claims to fetch.
     */
    orderBy?: ClaimOrderByWithRelationInput | ClaimOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Claims.
     */
    cursor?: ClaimWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Claims from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Claims.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Claims.
     */
    distinct?: ClaimScalarFieldEnum | ClaimScalarFieldEnum[]
  }

  /**
   * Claim findMany
   */
  export type ClaimFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    /**
     * Filter, which Claims to fetch.
     */
    where?: ClaimWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Claims to fetch.
     */
    orderBy?: ClaimOrderByWithRelationInput | ClaimOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Claims.
     */
    cursor?: ClaimWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Claims from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Claims.
     */
    skip?: number
    distinct?: ClaimScalarFieldEnum | ClaimScalarFieldEnum[]
  }

  /**
   * Claim create
   */
  export type ClaimCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    /**
     * The data needed to create a Claim.
     */
    data: XOR<ClaimCreateInput, ClaimUncheckedCreateInput>
  }

  /**
   * Claim createMany
   */
  export type ClaimCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Claims.
     */
    data: ClaimCreateManyInput | ClaimCreateManyInput[]
  }

  /**
   * Claim createManyAndReturn
   */
  export type ClaimCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * The data used to create many Claims.
     */
    data: ClaimCreateManyInput | ClaimCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Claim update
   */
  export type ClaimUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    /**
     * The data needed to update a Claim.
     */
    data: XOR<ClaimUpdateInput, ClaimUncheckedUpdateInput>
    /**
     * Choose, which Claim to update.
     */
    where: ClaimWhereUniqueInput
  }

  /**
   * Claim updateMany
   */
  export type ClaimUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Claims.
     */
    data: XOR<ClaimUpdateManyMutationInput, ClaimUncheckedUpdateManyInput>
    /**
     * Filter which Claims to update
     */
    where?: ClaimWhereInput
    /**
     * Limit how many Claims to update.
     */
    limit?: number
  }

  /**
   * Claim updateManyAndReturn
   */
  export type ClaimUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * The data used to update Claims.
     */
    data: XOR<ClaimUpdateManyMutationInput, ClaimUncheckedUpdateManyInput>
    /**
     * Filter which Claims to update
     */
    where?: ClaimWhereInput
    /**
     * Limit how many Claims to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Claim upsert
   */
  export type ClaimUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    /**
     * The filter to search for the Claim to update in case it exists.
     */
    where: ClaimWhereUniqueInput
    /**
     * In case the Claim found by the `where` argument doesn't exist, create a new Claim with this data.
     */
    create: XOR<ClaimCreateInput, ClaimUncheckedCreateInput>
    /**
     * In case the Claim was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ClaimUpdateInput, ClaimUncheckedUpdateInput>
  }

  /**
   * Claim delete
   */
  export type ClaimDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
    /**
     * Filter which Claim to delete.
     */
    where: ClaimWhereUniqueInput
  }

  /**
   * Claim deleteMany
   */
  export type ClaimDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Claims to delete
     */
    where?: ClaimWhereInput
    /**
     * Limit how many Claims to delete.
     */
    limit?: number
  }

  /**
   * Claim.documents
   */
  export type Claim$documentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    where?: ClaimDocumentWhereInput
    orderBy?: ClaimDocumentOrderByWithRelationInput | ClaimDocumentOrderByWithRelationInput[]
    cursor?: ClaimDocumentWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ClaimDocumentScalarFieldEnum | ClaimDocumentScalarFieldEnum[]
  }

  /**
   * Claim.comments
   */
  export type Claim$commentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    where?: ClaimCommentWhereInput
    orderBy?: ClaimCommentOrderByWithRelationInput | ClaimCommentOrderByWithRelationInput[]
    cursor?: ClaimCommentWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ClaimCommentScalarFieldEnum | ClaimCommentScalarFieldEnum[]
  }

  /**
   * Claim without action
   */
  export type ClaimDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Claim
     */
    select?: ClaimSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Claim
     */
    omit?: ClaimOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimInclude<ExtArgs> | null
  }


  /**
   * Model ClaimDocument
   */

  export type AggregateClaimDocument = {
    _count: ClaimDocumentCountAggregateOutputType | null
    _avg: ClaimDocumentAvgAggregateOutputType | null
    _sum: ClaimDocumentSumAggregateOutputType | null
    _min: ClaimDocumentMinAggregateOutputType | null
    _max: ClaimDocumentMaxAggregateOutputType | null
  }

  export type ClaimDocumentAvgAggregateOutputType = {
    id: number | null
    claimId: number | null
  }

  export type ClaimDocumentSumAggregateOutputType = {
    id: number | null
    claimId: number | null
  }

  export type ClaimDocumentMinAggregateOutputType = {
    id: number | null
    claimId: number | null
    fileName: string | null
    filePath: string | null
    uploadedAt: Date | null
  }

  export type ClaimDocumentMaxAggregateOutputType = {
    id: number | null
    claimId: number | null
    fileName: string | null
    filePath: string | null
    uploadedAt: Date | null
  }

  export type ClaimDocumentCountAggregateOutputType = {
    id: number
    claimId: number
    fileName: number
    filePath: number
    uploadedAt: number
    _all: number
  }


  export type ClaimDocumentAvgAggregateInputType = {
    id?: true
    claimId?: true
  }

  export type ClaimDocumentSumAggregateInputType = {
    id?: true
    claimId?: true
  }

  export type ClaimDocumentMinAggregateInputType = {
    id?: true
    claimId?: true
    fileName?: true
    filePath?: true
    uploadedAt?: true
  }

  export type ClaimDocumentMaxAggregateInputType = {
    id?: true
    claimId?: true
    fileName?: true
    filePath?: true
    uploadedAt?: true
  }

  export type ClaimDocumentCountAggregateInputType = {
    id?: true
    claimId?: true
    fileName?: true
    filePath?: true
    uploadedAt?: true
    _all?: true
  }

  export type ClaimDocumentAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ClaimDocument to aggregate.
     */
    where?: ClaimDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ClaimDocuments to fetch.
     */
    orderBy?: ClaimDocumentOrderByWithRelationInput | ClaimDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ClaimDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ClaimDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ClaimDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned ClaimDocuments
    **/
    _count?: true | ClaimDocumentCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: ClaimDocumentAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: ClaimDocumentSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ClaimDocumentMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ClaimDocumentMaxAggregateInputType
  }

  export type GetClaimDocumentAggregateType<T extends ClaimDocumentAggregateArgs> = {
        [P in keyof T & keyof AggregateClaimDocument]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateClaimDocument[P]>
      : GetScalarType<T[P], AggregateClaimDocument[P]>
  }




  export type ClaimDocumentGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ClaimDocumentWhereInput
    orderBy?: ClaimDocumentOrderByWithAggregationInput | ClaimDocumentOrderByWithAggregationInput[]
    by: ClaimDocumentScalarFieldEnum[] | ClaimDocumentScalarFieldEnum
    having?: ClaimDocumentScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ClaimDocumentCountAggregateInputType | true
    _avg?: ClaimDocumentAvgAggregateInputType
    _sum?: ClaimDocumentSumAggregateInputType
    _min?: ClaimDocumentMinAggregateInputType
    _max?: ClaimDocumentMaxAggregateInputType
  }

  export type ClaimDocumentGroupByOutputType = {
    id: number
    claimId: number
    fileName: string
    filePath: string
    uploadedAt: Date
    _count: ClaimDocumentCountAggregateOutputType | null
    _avg: ClaimDocumentAvgAggregateOutputType | null
    _sum: ClaimDocumentSumAggregateOutputType | null
    _min: ClaimDocumentMinAggregateOutputType | null
    _max: ClaimDocumentMaxAggregateOutputType | null
  }

  type GetClaimDocumentGroupByPayload<T extends ClaimDocumentGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ClaimDocumentGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ClaimDocumentGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ClaimDocumentGroupByOutputType[P]>
            : GetScalarType<T[P], ClaimDocumentGroupByOutputType[P]>
        }
      >
    >


  export type ClaimDocumentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    claimId?: boolean
    fileName?: boolean
    filePath?: boolean
    uploadedAt?: boolean
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["claimDocument"]>

  export type ClaimDocumentSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    claimId?: boolean
    fileName?: boolean
    filePath?: boolean
    uploadedAt?: boolean
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["claimDocument"]>

  export type ClaimDocumentSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    claimId?: boolean
    fileName?: boolean
    filePath?: boolean
    uploadedAt?: boolean
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["claimDocument"]>

  export type ClaimDocumentSelectScalar = {
    id?: boolean
    claimId?: boolean
    fileName?: boolean
    filePath?: boolean
    uploadedAt?: boolean
  }

  export type ClaimDocumentOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "claimId" | "fileName" | "filePath" | "uploadedAt", ExtArgs["result"]["claimDocument"]>
  export type ClaimDocumentInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }
  export type ClaimDocumentIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }
  export type ClaimDocumentIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }

  export type $ClaimDocumentPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "ClaimDocument"
    objects: {
      claim: Prisma.$ClaimPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      claimId: number
      fileName: string
      filePath: string
      uploadedAt: Date
    }, ExtArgs["result"]["claimDocument"]>
    composites: {}
  }

  type ClaimDocumentGetPayload<S extends boolean | null | undefined | ClaimDocumentDefaultArgs> = $Result.GetResult<Prisma.$ClaimDocumentPayload, S>

  type ClaimDocumentCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ClaimDocumentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ClaimDocumentCountAggregateInputType | true
    }

  export interface ClaimDocumentDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ClaimDocument'], meta: { name: 'ClaimDocument' } }
    /**
     * Find zero or one ClaimDocument that matches the filter.
     * @param {ClaimDocumentFindUniqueArgs} args - Arguments to find a ClaimDocument
     * @example
     * // Get one ClaimDocument
     * const claimDocument = await prisma.claimDocument.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ClaimDocumentFindUniqueArgs>(args: SelectSubset<T, ClaimDocumentFindUniqueArgs<ExtArgs>>): Prisma__ClaimDocumentClient<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one ClaimDocument that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ClaimDocumentFindUniqueOrThrowArgs} args - Arguments to find a ClaimDocument
     * @example
     * // Get one ClaimDocument
     * const claimDocument = await prisma.claimDocument.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ClaimDocumentFindUniqueOrThrowArgs>(args: SelectSubset<T, ClaimDocumentFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ClaimDocumentClient<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ClaimDocument that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimDocumentFindFirstArgs} args - Arguments to find a ClaimDocument
     * @example
     * // Get one ClaimDocument
     * const claimDocument = await prisma.claimDocument.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ClaimDocumentFindFirstArgs>(args?: SelectSubset<T, ClaimDocumentFindFirstArgs<ExtArgs>>): Prisma__ClaimDocumentClient<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ClaimDocument that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimDocumentFindFirstOrThrowArgs} args - Arguments to find a ClaimDocument
     * @example
     * // Get one ClaimDocument
     * const claimDocument = await prisma.claimDocument.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ClaimDocumentFindFirstOrThrowArgs>(args?: SelectSubset<T, ClaimDocumentFindFirstOrThrowArgs<ExtArgs>>): Prisma__ClaimDocumentClient<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more ClaimDocuments that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimDocumentFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all ClaimDocuments
     * const claimDocuments = await prisma.claimDocument.findMany()
     * 
     * // Get first 10 ClaimDocuments
     * const claimDocuments = await prisma.claimDocument.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const claimDocumentWithIdOnly = await prisma.claimDocument.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ClaimDocumentFindManyArgs>(args?: SelectSubset<T, ClaimDocumentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a ClaimDocument.
     * @param {ClaimDocumentCreateArgs} args - Arguments to create a ClaimDocument.
     * @example
     * // Create one ClaimDocument
     * const ClaimDocument = await prisma.claimDocument.create({
     *   data: {
     *     // ... data to create a ClaimDocument
     *   }
     * })
     * 
     */
    create<T extends ClaimDocumentCreateArgs>(args: SelectSubset<T, ClaimDocumentCreateArgs<ExtArgs>>): Prisma__ClaimDocumentClient<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many ClaimDocuments.
     * @param {ClaimDocumentCreateManyArgs} args - Arguments to create many ClaimDocuments.
     * @example
     * // Create many ClaimDocuments
     * const claimDocument = await prisma.claimDocument.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ClaimDocumentCreateManyArgs>(args?: SelectSubset<T, ClaimDocumentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many ClaimDocuments and returns the data saved in the database.
     * @param {ClaimDocumentCreateManyAndReturnArgs} args - Arguments to create many ClaimDocuments.
     * @example
     * // Create many ClaimDocuments
     * const claimDocument = await prisma.claimDocument.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many ClaimDocuments and only return the `id`
     * const claimDocumentWithIdOnly = await prisma.claimDocument.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ClaimDocumentCreateManyAndReturnArgs>(args?: SelectSubset<T, ClaimDocumentCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a ClaimDocument.
     * @param {ClaimDocumentDeleteArgs} args - Arguments to delete one ClaimDocument.
     * @example
     * // Delete one ClaimDocument
     * const ClaimDocument = await prisma.claimDocument.delete({
     *   where: {
     *     // ... filter to delete one ClaimDocument
     *   }
     * })
     * 
     */
    delete<T extends ClaimDocumentDeleteArgs>(args: SelectSubset<T, ClaimDocumentDeleteArgs<ExtArgs>>): Prisma__ClaimDocumentClient<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one ClaimDocument.
     * @param {ClaimDocumentUpdateArgs} args - Arguments to update one ClaimDocument.
     * @example
     * // Update one ClaimDocument
     * const claimDocument = await prisma.claimDocument.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ClaimDocumentUpdateArgs>(args: SelectSubset<T, ClaimDocumentUpdateArgs<ExtArgs>>): Prisma__ClaimDocumentClient<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more ClaimDocuments.
     * @param {ClaimDocumentDeleteManyArgs} args - Arguments to filter ClaimDocuments to delete.
     * @example
     * // Delete a few ClaimDocuments
     * const { count } = await prisma.claimDocument.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ClaimDocumentDeleteManyArgs>(args?: SelectSubset<T, ClaimDocumentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ClaimDocuments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimDocumentUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many ClaimDocuments
     * const claimDocument = await prisma.claimDocument.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ClaimDocumentUpdateManyArgs>(args: SelectSubset<T, ClaimDocumentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ClaimDocuments and returns the data updated in the database.
     * @param {ClaimDocumentUpdateManyAndReturnArgs} args - Arguments to update many ClaimDocuments.
     * @example
     * // Update many ClaimDocuments
     * const claimDocument = await prisma.claimDocument.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more ClaimDocuments and only return the `id`
     * const claimDocumentWithIdOnly = await prisma.claimDocument.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ClaimDocumentUpdateManyAndReturnArgs>(args: SelectSubset<T, ClaimDocumentUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one ClaimDocument.
     * @param {ClaimDocumentUpsertArgs} args - Arguments to update or create a ClaimDocument.
     * @example
     * // Update or create a ClaimDocument
     * const claimDocument = await prisma.claimDocument.upsert({
     *   create: {
     *     // ... data to create a ClaimDocument
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the ClaimDocument we want to update
     *   }
     * })
     */
    upsert<T extends ClaimDocumentUpsertArgs>(args: SelectSubset<T, ClaimDocumentUpsertArgs<ExtArgs>>): Prisma__ClaimDocumentClient<$Result.GetResult<Prisma.$ClaimDocumentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of ClaimDocuments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimDocumentCountArgs} args - Arguments to filter ClaimDocuments to count.
     * @example
     * // Count the number of ClaimDocuments
     * const count = await prisma.claimDocument.count({
     *   where: {
     *     // ... the filter for the ClaimDocuments we want to count
     *   }
     * })
    **/
    count<T extends ClaimDocumentCountArgs>(
      args?: Subset<T, ClaimDocumentCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ClaimDocumentCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a ClaimDocument.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimDocumentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ClaimDocumentAggregateArgs>(args: Subset<T, ClaimDocumentAggregateArgs>): Prisma.PrismaPromise<GetClaimDocumentAggregateType<T>>

    /**
     * Group by ClaimDocument.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimDocumentGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ClaimDocumentGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ClaimDocumentGroupByArgs['orderBy'] }
        : { orderBy?: ClaimDocumentGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ClaimDocumentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetClaimDocumentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ClaimDocument model
   */
  readonly fields: ClaimDocumentFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for ClaimDocument.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ClaimDocumentClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    claim<T extends ClaimDefaultArgs<ExtArgs> = {}>(args?: Subset<T, ClaimDefaultArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the ClaimDocument model
   */
  interface ClaimDocumentFieldRefs {
    readonly id: FieldRef<"ClaimDocument", 'Int'>
    readonly claimId: FieldRef<"ClaimDocument", 'Int'>
    readonly fileName: FieldRef<"ClaimDocument", 'String'>
    readonly filePath: FieldRef<"ClaimDocument", 'String'>
    readonly uploadedAt: FieldRef<"ClaimDocument", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * ClaimDocument findUnique
   */
  export type ClaimDocumentFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimDocument to fetch.
     */
    where: ClaimDocumentWhereUniqueInput
  }

  /**
   * ClaimDocument findUniqueOrThrow
   */
  export type ClaimDocumentFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimDocument to fetch.
     */
    where: ClaimDocumentWhereUniqueInput
  }

  /**
   * ClaimDocument findFirst
   */
  export type ClaimDocumentFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimDocument to fetch.
     */
    where?: ClaimDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ClaimDocuments to fetch.
     */
    orderBy?: ClaimDocumentOrderByWithRelationInput | ClaimDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ClaimDocuments.
     */
    cursor?: ClaimDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ClaimDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ClaimDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ClaimDocuments.
     */
    distinct?: ClaimDocumentScalarFieldEnum | ClaimDocumentScalarFieldEnum[]
  }

  /**
   * ClaimDocument findFirstOrThrow
   */
  export type ClaimDocumentFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimDocument to fetch.
     */
    where?: ClaimDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ClaimDocuments to fetch.
     */
    orderBy?: ClaimDocumentOrderByWithRelationInput | ClaimDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ClaimDocuments.
     */
    cursor?: ClaimDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ClaimDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ClaimDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ClaimDocuments.
     */
    distinct?: ClaimDocumentScalarFieldEnum | ClaimDocumentScalarFieldEnum[]
  }

  /**
   * ClaimDocument findMany
   */
  export type ClaimDocumentFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimDocuments to fetch.
     */
    where?: ClaimDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ClaimDocuments to fetch.
     */
    orderBy?: ClaimDocumentOrderByWithRelationInput | ClaimDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing ClaimDocuments.
     */
    cursor?: ClaimDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ClaimDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ClaimDocuments.
     */
    skip?: number
    distinct?: ClaimDocumentScalarFieldEnum | ClaimDocumentScalarFieldEnum[]
  }

  /**
   * ClaimDocument create
   */
  export type ClaimDocumentCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    /**
     * The data needed to create a ClaimDocument.
     */
    data: XOR<ClaimDocumentCreateInput, ClaimDocumentUncheckedCreateInput>
  }

  /**
   * ClaimDocument createMany
   */
  export type ClaimDocumentCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many ClaimDocuments.
     */
    data: ClaimDocumentCreateManyInput | ClaimDocumentCreateManyInput[]
  }

  /**
   * ClaimDocument createManyAndReturn
   */
  export type ClaimDocumentCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * The data used to create many ClaimDocuments.
     */
    data: ClaimDocumentCreateManyInput | ClaimDocumentCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * ClaimDocument update
   */
  export type ClaimDocumentUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    /**
     * The data needed to update a ClaimDocument.
     */
    data: XOR<ClaimDocumentUpdateInput, ClaimDocumentUncheckedUpdateInput>
    /**
     * Choose, which ClaimDocument to update.
     */
    where: ClaimDocumentWhereUniqueInput
  }

  /**
   * ClaimDocument updateMany
   */
  export type ClaimDocumentUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update ClaimDocuments.
     */
    data: XOR<ClaimDocumentUpdateManyMutationInput, ClaimDocumentUncheckedUpdateManyInput>
    /**
     * Filter which ClaimDocuments to update
     */
    where?: ClaimDocumentWhereInput
    /**
     * Limit how many ClaimDocuments to update.
     */
    limit?: number
  }

  /**
   * ClaimDocument updateManyAndReturn
   */
  export type ClaimDocumentUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * The data used to update ClaimDocuments.
     */
    data: XOR<ClaimDocumentUpdateManyMutationInput, ClaimDocumentUncheckedUpdateManyInput>
    /**
     * Filter which ClaimDocuments to update
     */
    where?: ClaimDocumentWhereInput
    /**
     * Limit how many ClaimDocuments to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * ClaimDocument upsert
   */
  export type ClaimDocumentUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    /**
     * The filter to search for the ClaimDocument to update in case it exists.
     */
    where: ClaimDocumentWhereUniqueInput
    /**
     * In case the ClaimDocument found by the `where` argument doesn't exist, create a new ClaimDocument with this data.
     */
    create: XOR<ClaimDocumentCreateInput, ClaimDocumentUncheckedCreateInput>
    /**
     * In case the ClaimDocument was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ClaimDocumentUpdateInput, ClaimDocumentUncheckedUpdateInput>
  }

  /**
   * ClaimDocument delete
   */
  export type ClaimDocumentDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
    /**
     * Filter which ClaimDocument to delete.
     */
    where: ClaimDocumentWhereUniqueInput
  }

  /**
   * ClaimDocument deleteMany
   */
  export type ClaimDocumentDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ClaimDocuments to delete
     */
    where?: ClaimDocumentWhereInput
    /**
     * Limit how many ClaimDocuments to delete.
     */
    limit?: number
  }

  /**
   * ClaimDocument without action
   */
  export type ClaimDocumentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimDocument
     */
    select?: ClaimDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimDocument
     */
    omit?: ClaimDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimDocumentInclude<ExtArgs> | null
  }


  /**
   * Model ClaimComment
   */

  export type AggregateClaimComment = {
    _count: ClaimCommentCountAggregateOutputType | null
    _avg: ClaimCommentAvgAggregateOutputType | null
    _sum: ClaimCommentSumAggregateOutputType | null
    _min: ClaimCommentMinAggregateOutputType | null
    _max: ClaimCommentMaxAggregateOutputType | null
  }

  export type ClaimCommentAvgAggregateOutputType = {
    id: number | null
    claimId: number | null
  }

  export type ClaimCommentSumAggregateOutputType = {
    id: number | null
    claimId: number | null
  }

  export type ClaimCommentMinAggregateOutputType = {
    id: number | null
    claimId: number | null
    text: string | null
    createdAt: Date | null
  }

  export type ClaimCommentMaxAggregateOutputType = {
    id: number | null
    claimId: number | null
    text: string | null
    createdAt: Date | null
  }

  export type ClaimCommentCountAggregateOutputType = {
    id: number
    claimId: number
    text: number
    createdAt: number
    _all: number
  }


  export type ClaimCommentAvgAggregateInputType = {
    id?: true
    claimId?: true
  }

  export type ClaimCommentSumAggregateInputType = {
    id?: true
    claimId?: true
  }

  export type ClaimCommentMinAggregateInputType = {
    id?: true
    claimId?: true
    text?: true
    createdAt?: true
  }

  export type ClaimCommentMaxAggregateInputType = {
    id?: true
    claimId?: true
    text?: true
    createdAt?: true
  }

  export type ClaimCommentCountAggregateInputType = {
    id?: true
    claimId?: true
    text?: true
    createdAt?: true
    _all?: true
  }

  export type ClaimCommentAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ClaimComment to aggregate.
     */
    where?: ClaimCommentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ClaimComments to fetch.
     */
    orderBy?: ClaimCommentOrderByWithRelationInput | ClaimCommentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ClaimCommentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ClaimComments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ClaimComments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned ClaimComments
    **/
    _count?: true | ClaimCommentCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: ClaimCommentAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: ClaimCommentSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ClaimCommentMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ClaimCommentMaxAggregateInputType
  }

  export type GetClaimCommentAggregateType<T extends ClaimCommentAggregateArgs> = {
        [P in keyof T & keyof AggregateClaimComment]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateClaimComment[P]>
      : GetScalarType<T[P], AggregateClaimComment[P]>
  }




  export type ClaimCommentGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ClaimCommentWhereInput
    orderBy?: ClaimCommentOrderByWithAggregationInput | ClaimCommentOrderByWithAggregationInput[]
    by: ClaimCommentScalarFieldEnum[] | ClaimCommentScalarFieldEnum
    having?: ClaimCommentScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ClaimCommentCountAggregateInputType | true
    _avg?: ClaimCommentAvgAggregateInputType
    _sum?: ClaimCommentSumAggregateInputType
    _min?: ClaimCommentMinAggregateInputType
    _max?: ClaimCommentMaxAggregateInputType
  }

  export type ClaimCommentGroupByOutputType = {
    id: number
    claimId: number
    text: string
    createdAt: Date
    _count: ClaimCommentCountAggregateOutputType | null
    _avg: ClaimCommentAvgAggregateOutputType | null
    _sum: ClaimCommentSumAggregateOutputType | null
    _min: ClaimCommentMinAggregateOutputType | null
    _max: ClaimCommentMaxAggregateOutputType | null
  }

  type GetClaimCommentGroupByPayload<T extends ClaimCommentGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ClaimCommentGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ClaimCommentGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ClaimCommentGroupByOutputType[P]>
            : GetScalarType<T[P], ClaimCommentGroupByOutputType[P]>
        }
      >
    >


  export type ClaimCommentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    claimId?: boolean
    text?: boolean
    createdAt?: boolean
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["claimComment"]>

  export type ClaimCommentSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    claimId?: boolean
    text?: boolean
    createdAt?: boolean
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["claimComment"]>

  export type ClaimCommentSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    claimId?: boolean
    text?: boolean
    createdAt?: boolean
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["claimComment"]>

  export type ClaimCommentSelectScalar = {
    id?: boolean
    claimId?: boolean
    text?: boolean
    createdAt?: boolean
  }

  export type ClaimCommentOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "claimId" | "text" | "createdAt", ExtArgs["result"]["claimComment"]>
  export type ClaimCommentInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }
  export type ClaimCommentIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }
  export type ClaimCommentIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    claim?: boolean | ClaimDefaultArgs<ExtArgs>
  }

  export type $ClaimCommentPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "ClaimComment"
    objects: {
      claim: Prisma.$ClaimPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      claimId: number
      text: string
      createdAt: Date
    }, ExtArgs["result"]["claimComment"]>
    composites: {}
  }

  type ClaimCommentGetPayload<S extends boolean | null | undefined | ClaimCommentDefaultArgs> = $Result.GetResult<Prisma.$ClaimCommentPayload, S>

  type ClaimCommentCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ClaimCommentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ClaimCommentCountAggregateInputType | true
    }

  export interface ClaimCommentDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ClaimComment'], meta: { name: 'ClaimComment' } }
    /**
     * Find zero or one ClaimComment that matches the filter.
     * @param {ClaimCommentFindUniqueArgs} args - Arguments to find a ClaimComment
     * @example
     * // Get one ClaimComment
     * const claimComment = await prisma.claimComment.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ClaimCommentFindUniqueArgs>(args: SelectSubset<T, ClaimCommentFindUniqueArgs<ExtArgs>>): Prisma__ClaimCommentClient<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one ClaimComment that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ClaimCommentFindUniqueOrThrowArgs} args - Arguments to find a ClaimComment
     * @example
     * // Get one ClaimComment
     * const claimComment = await prisma.claimComment.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ClaimCommentFindUniqueOrThrowArgs>(args: SelectSubset<T, ClaimCommentFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ClaimCommentClient<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ClaimComment that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimCommentFindFirstArgs} args - Arguments to find a ClaimComment
     * @example
     * // Get one ClaimComment
     * const claimComment = await prisma.claimComment.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ClaimCommentFindFirstArgs>(args?: SelectSubset<T, ClaimCommentFindFirstArgs<ExtArgs>>): Prisma__ClaimCommentClient<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ClaimComment that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimCommentFindFirstOrThrowArgs} args - Arguments to find a ClaimComment
     * @example
     * // Get one ClaimComment
     * const claimComment = await prisma.claimComment.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ClaimCommentFindFirstOrThrowArgs>(args?: SelectSubset<T, ClaimCommentFindFirstOrThrowArgs<ExtArgs>>): Prisma__ClaimCommentClient<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more ClaimComments that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimCommentFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all ClaimComments
     * const claimComments = await prisma.claimComment.findMany()
     * 
     * // Get first 10 ClaimComments
     * const claimComments = await prisma.claimComment.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const claimCommentWithIdOnly = await prisma.claimComment.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ClaimCommentFindManyArgs>(args?: SelectSubset<T, ClaimCommentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a ClaimComment.
     * @param {ClaimCommentCreateArgs} args - Arguments to create a ClaimComment.
     * @example
     * // Create one ClaimComment
     * const ClaimComment = await prisma.claimComment.create({
     *   data: {
     *     // ... data to create a ClaimComment
     *   }
     * })
     * 
     */
    create<T extends ClaimCommentCreateArgs>(args: SelectSubset<T, ClaimCommentCreateArgs<ExtArgs>>): Prisma__ClaimCommentClient<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many ClaimComments.
     * @param {ClaimCommentCreateManyArgs} args - Arguments to create many ClaimComments.
     * @example
     * // Create many ClaimComments
     * const claimComment = await prisma.claimComment.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ClaimCommentCreateManyArgs>(args?: SelectSubset<T, ClaimCommentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many ClaimComments and returns the data saved in the database.
     * @param {ClaimCommentCreateManyAndReturnArgs} args - Arguments to create many ClaimComments.
     * @example
     * // Create many ClaimComments
     * const claimComment = await prisma.claimComment.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many ClaimComments and only return the `id`
     * const claimCommentWithIdOnly = await prisma.claimComment.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ClaimCommentCreateManyAndReturnArgs>(args?: SelectSubset<T, ClaimCommentCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a ClaimComment.
     * @param {ClaimCommentDeleteArgs} args - Arguments to delete one ClaimComment.
     * @example
     * // Delete one ClaimComment
     * const ClaimComment = await prisma.claimComment.delete({
     *   where: {
     *     // ... filter to delete one ClaimComment
     *   }
     * })
     * 
     */
    delete<T extends ClaimCommentDeleteArgs>(args: SelectSubset<T, ClaimCommentDeleteArgs<ExtArgs>>): Prisma__ClaimCommentClient<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one ClaimComment.
     * @param {ClaimCommentUpdateArgs} args - Arguments to update one ClaimComment.
     * @example
     * // Update one ClaimComment
     * const claimComment = await prisma.claimComment.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ClaimCommentUpdateArgs>(args: SelectSubset<T, ClaimCommentUpdateArgs<ExtArgs>>): Prisma__ClaimCommentClient<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more ClaimComments.
     * @param {ClaimCommentDeleteManyArgs} args - Arguments to filter ClaimComments to delete.
     * @example
     * // Delete a few ClaimComments
     * const { count } = await prisma.claimComment.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ClaimCommentDeleteManyArgs>(args?: SelectSubset<T, ClaimCommentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ClaimComments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimCommentUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many ClaimComments
     * const claimComment = await prisma.claimComment.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ClaimCommentUpdateManyArgs>(args: SelectSubset<T, ClaimCommentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ClaimComments and returns the data updated in the database.
     * @param {ClaimCommentUpdateManyAndReturnArgs} args - Arguments to update many ClaimComments.
     * @example
     * // Update many ClaimComments
     * const claimComment = await prisma.claimComment.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more ClaimComments and only return the `id`
     * const claimCommentWithIdOnly = await prisma.claimComment.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ClaimCommentUpdateManyAndReturnArgs>(args: SelectSubset<T, ClaimCommentUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one ClaimComment.
     * @param {ClaimCommentUpsertArgs} args - Arguments to update or create a ClaimComment.
     * @example
     * // Update or create a ClaimComment
     * const claimComment = await prisma.claimComment.upsert({
     *   create: {
     *     // ... data to create a ClaimComment
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the ClaimComment we want to update
     *   }
     * })
     */
    upsert<T extends ClaimCommentUpsertArgs>(args: SelectSubset<T, ClaimCommentUpsertArgs<ExtArgs>>): Prisma__ClaimCommentClient<$Result.GetResult<Prisma.$ClaimCommentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of ClaimComments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimCommentCountArgs} args - Arguments to filter ClaimComments to count.
     * @example
     * // Count the number of ClaimComments
     * const count = await prisma.claimComment.count({
     *   where: {
     *     // ... the filter for the ClaimComments we want to count
     *   }
     * })
    **/
    count<T extends ClaimCommentCountArgs>(
      args?: Subset<T, ClaimCommentCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ClaimCommentCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a ClaimComment.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimCommentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ClaimCommentAggregateArgs>(args: Subset<T, ClaimCommentAggregateArgs>): Prisma.PrismaPromise<GetClaimCommentAggregateType<T>>

    /**
     * Group by ClaimComment.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClaimCommentGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ClaimCommentGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ClaimCommentGroupByArgs['orderBy'] }
        : { orderBy?: ClaimCommentGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ClaimCommentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetClaimCommentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ClaimComment model
   */
  readonly fields: ClaimCommentFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for ClaimComment.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ClaimCommentClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    claim<T extends ClaimDefaultArgs<ExtArgs> = {}>(args?: Subset<T, ClaimDefaultArgs<ExtArgs>>): Prisma__ClaimClient<$Result.GetResult<Prisma.$ClaimPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the ClaimComment model
   */
  interface ClaimCommentFieldRefs {
    readonly id: FieldRef<"ClaimComment", 'Int'>
    readonly claimId: FieldRef<"ClaimComment", 'Int'>
    readonly text: FieldRef<"ClaimComment", 'String'>
    readonly createdAt: FieldRef<"ClaimComment", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * ClaimComment findUnique
   */
  export type ClaimCommentFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimComment to fetch.
     */
    where: ClaimCommentWhereUniqueInput
  }

  /**
   * ClaimComment findUniqueOrThrow
   */
  export type ClaimCommentFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimComment to fetch.
     */
    where: ClaimCommentWhereUniqueInput
  }

  /**
   * ClaimComment findFirst
   */
  export type ClaimCommentFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimComment to fetch.
     */
    where?: ClaimCommentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ClaimComments to fetch.
     */
    orderBy?: ClaimCommentOrderByWithRelationInput | ClaimCommentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ClaimComments.
     */
    cursor?: ClaimCommentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ClaimComments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ClaimComments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ClaimComments.
     */
    distinct?: ClaimCommentScalarFieldEnum | ClaimCommentScalarFieldEnum[]
  }

  /**
   * ClaimComment findFirstOrThrow
   */
  export type ClaimCommentFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimComment to fetch.
     */
    where?: ClaimCommentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ClaimComments to fetch.
     */
    orderBy?: ClaimCommentOrderByWithRelationInput | ClaimCommentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ClaimComments.
     */
    cursor?: ClaimCommentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ClaimComments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ClaimComments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ClaimComments.
     */
    distinct?: ClaimCommentScalarFieldEnum | ClaimCommentScalarFieldEnum[]
  }

  /**
   * ClaimComment findMany
   */
  export type ClaimCommentFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    /**
     * Filter, which ClaimComments to fetch.
     */
    where?: ClaimCommentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ClaimComments to fetch.
     */
    orderBy?: ClaimCommentOrderByWithRelationInput | ClaimCommentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing ClaimComments.
     */
    cursor?: ClaimCommentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ClaimComments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ClaimComments.
     */
    skip?: number
    distinct?: ClaimCommentScalarFieldEnum | ClaimCommentScalarFieldEnum[]
  }

  /**
   * ClaimComment create
   */
  export type ClaimCommentCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    /**
     * The data needed to create a ClaimComment.
     */
    data: XOR<ClaimCommentCreateInput, ClaimCommentUncheckedCreateInput>
  }

  /**
   * ClaimComment createMany
   */
  export type ClaimCommentCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many ClaimComments.
     */
    data: ClaimCommentCreateManyInput | ClaimCommentCreateManyInput[]
  }

  /**
   * ClaimComment createManyAndReturn
   */
  export type ClaimCommentCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * The data used to create many ClaimComments.
     */
    data: ClaimCommentCreateManyInput | ClaimCommentCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * ClaimComment update
   */
  export type ClaimCommentUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    /**
     * The data needed to update a ClaimComment.
     */
    data: XOR<ClaimCommentUpdateInput, ClaimCommentUncheckedUpdateInput>
    /**
     * Choose, which ClaimComment to update.
     */
    where: ClaimCommentWhereUniqueInput
  }

  /**
   * ClaimComment updateMany
   */
  export type ClaimCommentUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update ClaimComments.
     */
    data: XOR<ClaimCommentUpdateManyMutationInput, ClaimCommentUncheckedUpdateManyInput>
    /**
     * Filter which ClaimComments to update
     */
    where?: ClaimCommentWhereInput
    /**
     * Limit how many ClaimComments to update.
     */
    limit?: number
  }

  /**
   * ClaimComment updateManyAndReturn
   */
  export type ClaimCommentUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * The data used to update ClaimComments.
     */
    data: XOR<ClaimCommentUpdateManyMutationInput, ClaimCommentUncheckedUpdateManyInput>
    /**
     * Filter which ClaimComments to update
     */
    where?: ClaimCommentWhereInput
    /**
     * Limit how many ClaimComments to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * ClaimComment upsert
   */
  export type ClaimCommentUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    /**
     * The filter to search for the ClaimComment to update in case it exists.
     */
    where: ClaimCommentWhereUniqueInput
    /**
     * In case the ClaimComment found by the `where` argument doesn't exist, create a new ClaimComment with this data.
     */
    create: XOR<ClaimCommentCreateInput, ClaimCommentUncheckedCreateInput>
    /**
     * In case the ClaimComment was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ClaimCommentUpdateInput, ClaimCommentUncheckedUpdateInput>
  }

  /**
   * ClaimComment delete
   */
  export type ClaimCommentDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
    /**
     * Filter which ClaimComment to delete.
     */
    where: ClaimCommentWhereUniqueInput
  }

  /**
   * ClaimComment deleteMany
   */
  export type ClaimCommentDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ClaimComments to delete
     */
    where?: ClaimCommentWhereInput
    /**
     * Limit how many ClaimComments to delete.
     */
    limit?: number
  }

  /**
   * ClaimComment without action
   */
  export type ClaimCommentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClaimComment
     */
    select?: ClaimCommentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ClaimComment
     */
    omit?: ClaimCommentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClaimCommentInclude<ExtArgs> | null
  }


  /**
   * Model Policy
   */

  export type AggregatePolicy = {
    _count: PolicyCountAggregateOutputType | null
    _avg: PolicyAvgAggregateOutputType | null
    _sum: PolicySumAggregateOutputType | null
    _min: PolicyMinAggregateOutputType | null
    _max: PolicyMaxAggregateOutputType | null
  }

  export type PolicyAvgAggregateOutputType = {
    id: number | null
    employeeId: number | null
    issuedAge: number | null
    insuredCoverage: number | null
    spouseCoverage: number | null
  }

  export type PolicySumAggregateOutputType = {
    id: number | null
    employeeId: number | null
    issuedAge: number | null
    insuredCoverage: number | null
    spouseCoverage: number | null
  }

  export type PolicyMinAggregateOutputType = {
    id: number | null
    employeeId: number | null
    policyOwner: string | null
    insured: string | null
    spouse: string | null
    group: string | null
    policyNumber: string | null
    originalEffectiveDate: Date | null
    scheduledEffectiveDate: Date | null
    issuedAge: number | null
    insuredCoverage: number | null
    spouseCoverage: number | null
  }

  export type PolicyMaxAggregateOutputType = {
    id: number | null
    employeeId: number | null
    policyOwner: string | null
    insured: string | null
    spouse: string | null
    group: string | null
    policyNumber: string | null
    originalEffectiveDate: Date | null
    scheduledEffectiveDate: Date | null
    issuedAge: number | null
    insuredCoverage: number | null
    spouseCoverage: number | null
  }

  export type PolicyCountAggregateOutputType = {
    id: number
    employeeId: number
    policyOwner: number
    insured: number
    spouse: number
    group: number
    policyNumber: number
    originalEffectiveDate: number
    scheduledEffectiveDate: number
    issuedAge: number
    insuredCoverage: number
    spouseCoverage: number
    _all: number
  }


  export type PolicyAvgAggregateInputType = {
    id?: true
    employeeId?: true
    issuedAge?: true
    insuredCoverage?: true
    spouseCoverage?: true
  }

  export type PolicySumAggregateInputType = {
    id?: true
    employeeId?: true
    issuedAge?: true
    insuredCoverage?: true
    spouseCoverage?: true
  }

  export type PolicyMinAggregateInputType = {
    id?: true
    employeeId?: true
    policyOwner?: true
    insured?: true
    spouse?: true
    group?: true
    policyNumber?: true
    originalEffectiveDate?: true
    scheduledEffectiveDate?: true
    issuedAge?: true
    insuredCoverage?: true
    spouseCoverage?: true
  }

  export type PolicyMaxAggregateInputType = {
    id?: true
    employeeId?: true
    policyOwner?: true
    insured?: true
    spouse?: true
    group?: true
    policyNumber?: true
    originalEffectiveDate?: true
    scheduledEffectiveDate?: true
    issuedAge?: true
    insuredCoverage?: true
    spouseCoverage?: true
  }

  export type PolicyCountAggregateInputType = {
    id?: true
    employeeId?: true
    policyOwner?: true
    insured?: true
    spouse?: true
    group?: true
    policyNumber?: true
    originalEffectiveDate?: true
    scheduledEffectiveDate?: true
    issuedAge?: true
    insuredCoverage?: true
    spouseCoverage?: true
    _all?: true
  }

  export type PolicyAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Policy to aggregate.
     */
    where?: PolicyWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Policies to fetch.
     */
    orderBy?: PolicyOrderByWithRelationInput | PolicyOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: PolicyWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Policies from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Policies.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Policies
    **/
    _count?: true | PolicyCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: PolicyAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: PolicySumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: PolicyMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: PolicyMaxAggregateInputType
  }

  export type GetPolicyAggregateType<T extends PolicyAggregateArgs> = {
        [P in keyof T & keyof AggregatePolicy]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregatePolicy[P]>
      : GetScalarType<T[P], AggregatePolicy[P]>
  }




  export type PolicyGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PolicyWhereInput
    orderBy?: PolicyOrderByWithAggregationInput | PolicyOrderByWithAggregationInput[]
    by: PolicyScalarFieldEnum[] | PolicyScalarFieldEnum
    having?: PolicyScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: PolicyCountAggregateInputType | true
    _avg?: PolicyAvgAggregateInputType
    _sum?: PolicySumAggregateInputType
    _min?: PolicyMinAggregateInputType
    _max?: PolicyMaxAggregateInputType
  }

  export type PolicyGroupByOutputType = {
    id: number
    employeeId: number
    policyOwner: string
    insured: string
    spouse: string | null
    group: string
    policyNumber: string
    originalEffectiveDate: Date | null
    scheduledEffectiveDate: Date | null
    issuedAge: number | null
    insuredCoverage: number | null
    spouseCoverage: number | null
    _count: PolicyCountAggregateOutputType | null
    _avg: PolicyAvgAggregateOutputType | null
    _sum: PolicySumAggregateOutputType | null
    _min: PolicyMinAggregateOutputType | null
    _max: PolicyMaxAggregateOutputType | null
  }

  type GetPolicyGroupByPayload<T extends PolicyGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<PolicyGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof PolicyGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], PolicyGroupByOutputType[P]>
            : GetScalarType<T[P], PolicyGroupByOutputType[P]>
        }
      >
    >


  export type PolicySelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    employeeId?: boolean
    policyOwner?: boolean
    insured?: boolean
    spouse?: boolean
    group?: boolean
    policyNumber?: boolean
    originalEffectiveDate?: boolean
    scheduledEffectiveDate?: boolean
    issuedAge?: boolean
    insuredCoverage?: boolean
    spouseCoverage?: boolean
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
    documents?: boolean | Policy$documentsArgs<ExtArgs>
    _count?: boolean | PolicyCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["policy"]>

  export type PolicySelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    employeeId?: boolean
    policyOwner?: boolean
    insured?: boolean
    spouse?: boolean
    group?: boolean
    policyNumber?: boolean
    originalEffectiveDate?: boolean
    scheduledEffectiveDate?: boolean
    issuedAge?: boolean
    insuredCoverage?: boolean
    spouseCoverage?: boolean
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["policy"]>

  export type PolicySelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    employeeId?: boolean
    policyOwner?: boolean
    insured?: boolean
    spouse?: boolean
    group?: boolean
    policyNumber?: boolean
    originalEffectiveDate?: boolean
    scheduledEffectiveDate?: boolean
    issuedAge?: boolean
    insuredCoverage?: boolean
    spouseCoverage?: boolean
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["policy"]>

  export type PolicySelectScalar = {
    id?: boolean
    employeeId?: boolean
    policyOwner?: boolean
    insured?: boolean
    spouse?: boolean
    group?: boolean
    policyNumber?: boolean
    originalEffectiveDate?: boolean
    scheduledEffectiveDate?: boolean
    issuedAge?: boolean
    insuredCoverage?: boolean
    spouseCoverage?: boolean
  }

  export type PolicyOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "employeeId" | "policyOwner" | "insured" | "spouse" | "group" | "policyNumber" | "originalEffectiveDate" | "scheduledEffectiveDate" | "issuedAge" | "insuredCoverage" | "spouseCoverage", ExtArgs["result"]["policy"]>
  export type PolicyInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
    documents?: boolean | Policy$documentsArgs<ExtArgs>
    _count?: boolean | PolicyCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type PolicyIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
  }
  export type PolicyIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    employee?: boolean | EmployeeDefaultArgs<ExtArgs>
  }

  export type $PolicyPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Policy"
    objects: {
      employee: Prisma.$EmployeePayload<ExtArgs>
      documents: Prisma.$PolicyDocumentPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      employeeId: number
      policyOwner: string
      insured: string
      spouse: string | null
      group: string
      policyNumber: string
      originalEffectiveDate: Date | null
      scheduledEffectiveDate: Date | null
      issuedAge: number | null
      insuredCoverage: number | null
      spouseCoverage: number | null
    }, ExtArgs["result"]["policy"]>
    composites: {}
  }

  type PolicyGetPayload<S extends boolean | null | undefined | PolicyDefaultArgs> = $Result.GetResult<Prisma.$PolicyPayload, S>

  type PolicyCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<PolicyFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: PolicyCountAggregateInputType | true
    }

  export interface PolicyDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Policy'], meta: { name: 'Policy' } }
    /**
     * Find zero or one Policy that matches the filter.
     * @param {PolicyFindUniqueArgs} args - Arguments to find a Policy
     * @example
     * // Get one Policy
     * const policy = await prisma.policy.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends PolicyFindUniqueArgs>(args: SelectSubset<T, PolicyFindUniqueArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Policy that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {PolicyFindUniqueOrThrowArgs} args - Arguments to find a Policy
     * @example
     * // Get one Policy
     * const policy = await prisma.policy.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends PolicyFindUniqueOrThrowArgs>(args: SelectSubset<T, PolicyFindUniqueOrThrowArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Policy that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyFindFirstArgs} args - Arguments to find a Policy
     * @example
     * // Get one Policy
     * const policy = await prisma.policy.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends PolicyFindFirstArgs>(args?: SelectSubset<T, PolicyFindFirstArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Policy that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyFindFirstOrThrowArgs} args - Arguments to find a Policy
     * @example
     * // Get one Policy
     * const policy = await prisma.policy.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends PolicyFindFirstOrThrowArgs>(args?: SelectSubset<T, PolicyFindFirstOrThrowArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Policies that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Policies
     * const policies = await prisma.policy.findMany()
     * 
     * // Get first 10 Policies
     * const policies = await prisma.policy.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const policyWithIdOnly = await prisma.policy.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends PolicyFindManyArgs>(args?: SelectSubset<T, PolicyFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Policy.
     * @param {PolicyCreateArgs} args - Arguments to create a Policy.
     * @example
     * // Create one Policy
     * const Policy = await prisma.policy.create({
     *   data: {
     *     // ... data to create a Policy
     *   }
     * })
     * 
     */
    create<T extends PolicyCreateArgs>(args: SelectSubset<T, PolicyCreateArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Policies.
     * @param {PolicyCreateManyArgs} args - Arguments to create many Policies.
     * @example
     * // Create many Policies
     * const policy = await prisma.policy.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends PolicyCreateManyArgs>(args?: SelectSubset<T, PolicyCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Policies and returns the data saved in the database.
     * @param {PolicyCreateManyAndReturnArgs} args - Arguments to create many Policies.
     * @example
     * // Create many Policies
     * const policy = await prisma.policy.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Policies and only return the `id`
     * const policyWithIdOnly = await prisma.policy.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends PolicyCreateManyAndReturnArgs>(args?: SelectSubset<T, PolicyCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Policy.
     * @param {PolicyDeleteArgs} args - Arguments to delete one Policy.
     * @example
     * // Delete one Policy
     * const Policy = await prisma.policy.delete({
     *   where: {
     *     // ... filter to delete one Policy
     *   }
     * })
     * 
     */
    delete<T extends PolicyDeleteArgs>(args: SelectSubset<T, PolicyDeleteArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Policy.
     * @param {PolicyUpdateArgs} args - Arguments to update one Policy.
     * @example
     * // Update one Policy
     * const policy = await prisma.policy.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends PolicyUpdateArgs>(args: SelectSubset<T, PolicyUpdateArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Policies.
     * @param {PolicyDeleteManyArgs} args - Arguments to filter Policies to delete.
     * @example
     * // Delete a few Policies
     * const { count } = await prisma.policy.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends PolicyDeleteManyArgs>(args?: SelectSubset<T, PolicyDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Policies.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Policies
     * const policy = await prisma.policy.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends PolicyUpdateManyArgs>(args: SelectSubset<T, PolicyUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Policies and returns the data updated in the database.
     * @param {PolicyUpdateManyAndReturnArgs} args - Arguments to update many Policies.
     * @example
     * // Update many Policies
     * const policy = await prisma.policy.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Policies and only return the `id`
     * const policyWithIdOnly = await prisma.policy.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends PolicyUpdateManyAndReturnArgs>(args: SelectSubset<T, PolicyUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Policy.
     * @param {PolicyUpsertArgs} args - Arguments to update or create a Policy.
     * @example
     * // Update or create a Policy
     * const policy = await prisma.policy.upsert({
     *   create: {
     *     // ... data to create a Policy
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Policy we want to update
     *   }
     * })
     */
    upsert<T extends PolicyUpsertArgs>(args: SelectSubset<T, PolicyUpsertArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Policies.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyCountArgs} args - Arguments to filter Policies to count.
     * @example
     * // Count the number of Policies
     * const count = await prisma.policy.count({
     *   where: {
     *     // ... the filter for the Policies we want to count
     *   }
     * })
    **/
    count<T extends PolicyCountArgs>(
      args?: Subset<T, PolicyCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], PolicyCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Policy.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends PolicyAggregateArgs>(args: Subset<T, PolicyAggregateArgs>): Prisma.PrismaPromise<GetPolicyAggregateType<T>>

    /**
     * Group by Policy.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends PolicyGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: PolicyGroupByArgs['orderBy'] }
        : { orderBy?: PolicyGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, PolicyGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPolicyGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Policy model
   */
  readonly fields: PolicyFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Policy.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__PolicyClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    employee<T extends EmployeeDefaultArgs<ExtArgs> = {}>(args?: Subset<T, EmployeeDefaultArgs<ExtArgs>>): Prisma__EmployeeClient<$Result.GetResult<Prisma.$EmployeePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    documents<T extends Policy$documentsArgs<ExtArgs> = {}>(args?: Subset<T, Policy$documentsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Policy model
   */
  interface PolicyFieldRefs {
    readonly id: FieldRef<"Policy", 'Int'>
    readonly employeeId: FieldRef<"Policy", 'Int'>
    readonly policyOwner: FieldRef<"Policy", 'String'>
    readonly insured: FieldRef<"Policy", 'String'>
    readonly spouse: FieldRef<"Policy", 'String'>
    readonly group: FieldRef<"Policy", 'String'>
    readonly policyNumber: FieldRef<"Policy", 'String'>
    readonly originalEffectiveDate: FieldRef<"Policy", 'DateTime'>
    readonly scheduledEffectiveDate: FieldRef<"Policy", 'DateTime'>
    readonly issuedAge: FieldRef<"Policy", 'Int'>
    readonly insuredCoverage: FieldRef<"Policy", 'Float'>
    readonly spouseCoverage: FieldRef<"Policy", 'Float'>
  }
    

  // Custom InputTypes
  /**
   * Policy findUnique
   */
  export type PolicyFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    /**
     * Filter, which Policy to fetch.
     */
    where: PolicyWhereUniqueInput
  }

  /**
   * Policy findUniqueOrThrow
   */
  export type PolicyFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    /**
     * Filter, which Policy to fetch.
     */
    where: PolicyWhereUniqueInput
  }

  /**
   * Policy findFirst
   */
  export type PolicyFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    /**
     * Filter, which Policy to fetch.
     */
    where?: PolicyWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Policies to fetch.
     */
    orderBy?: PolicyOrderByWithRelationInput | PolicyOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Policies.
     */
    cursor?: PolicyWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Policies from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Policies.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Policies.
     */
    distinct?: PolicyScalarFieldEnum | PolicyScalarFieldEnum[]
  }

  /**
   * Policy findFirstOrThrow
   */
  export type PolicyFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    /**
     * Filter, which Policy to fetch.
     */
    where?: PolicyWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Policies to fetch.
     */
    orderBy?: PolicyOrderByWithRelationInput | PolicyOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Policies.
     */
    cursor?: PolicyWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Policies from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Policies.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Policies.
     */
    distinct?: PolicyScalarFieldEnum | PolicyScalarFieldEnum[]
  }

  /**
   * Policy findMany
   */
  export type PolicyFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    /**
     * Filter, which Policies to fetch.
     */
    where?: PolicyWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Policies to fetch.
     */
    orderBy?: PolicyOrderByWithRelationInput | PolicyOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Policies.
     */
    cursor?: PolicyWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Policies from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Policies.
     */
    skip?: number
    distinct?: PolicyScalarFieldEnum | PolicyScalarFieldEnum[]
  }

  /**
   * Policy create
   */
  export type PolicyCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    /**
     * The data needed to create a Policy.
     */
    data: XOR<PolicyCreateInput, PolicyUncheckedCreateInput>
  }

  /**
   * Policy createMany
   */
  export type PolicyCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Policies.
     */
    data: PolicyCreateManyInput | PolicyCreateManyInput[]
  }

  /**
   * Policy createManyAndReturn
   */
  export type PolicyCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * The data used to create many Policies.
     */
    data: PolicyCreateManyInput | PolicyCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Policy update
   */
  export type PolicyUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    /**
     * The data needed to update a Policy.
     */
    data: XOR<PolicyUpdateInput, PolicyUncheckedUpdateInput>
    /**
     * Choose, which Policy to update.
     */
    where: PolicyWhereUniqueInput
  }

  /**
   * Policy updateMany
   */
  export type PolicyUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Policies.
     */
    data: XOR<PolicyUpdateManyMutationInput, PolicyUncheckedUpdateManyInput>
    /**
     * Filter which Policies to update
     */
    where?: PolicyWhereInput
    /**
     * Limit how many Policies to update.
     */
    limit?: number
  }

  /**
   * Policy updateManyAndReturn
   */
  export type PolicyUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * The data used to update Policies.
     */
    data: XOR<PolicyUpdateManyMutationInput, PolicyUncheckedUpdateManyInput>
    /**
     * Filter which Policies to update
     */
    where?: PolicyWhereInput
    /**
     * Limit how many Policies to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Policy upsert
   */
  export type PolicyUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    /**
     * The filter to search for the Policy to update in case it exists.
     */
    where: PolicyWhereUniqueInput
    /**
     * In case the Policy found by the `where` argument doesn't exist, create a new Policy with this data.
     */
    create: XOR<PolicyCreateInput, PolicyUncheckedCreateInput>
    /**
     * In case the Policy was found with the provided `where` argument, update it with this data.
     */
    update: XOR<PolicyUpdateInput, PolicyUncheckedUpdateInput>
  }

  /**
   * Policy delete
   */
  export type PolicyDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
    /**
     * Filter which Policy to delete.
     */
    where: PolicyWhereUniqueInput
  }

  /**
   * Policy deleteMany
   */
  export type PolicyDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Policies to delete
     */
    where?: PolicyWhereInput
    /**
     * Limit how many Policies to delete.
     */
    limit?: number
  }

  /**
   * Policy.documents
   */
  export type Policy$documentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    where?: PolicyDocumentWhereInput
    orderBy?: PolicyDocumentOrderByWithRelationInput | PolicyDocumentOrderByWithRelationInput[]
    cursor?: PolicyDocumentWhereUniqueInput
    take?: number
    skip?: number
    distinct?: PolicyDocumentScalarFieldEnum | PolicyDocumentScalarFieldEnum[]
  }

  /**
   * Policy without action
   */
  export type PolicyDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Policy
     */
    select?: PolicySelect<ExtArgs> | null
    /**
     * Omit specific fields from the Policy
     */
    omit?: PolicyOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyInclude<ExtArgs> | null
  }


  /**
   * Model PolicyDocument
   */

  export type AggregatePolicyDocument = {
    _count: PolicyDocumentCountAggregateOutputType | null
    _avg: PolicyDocumentAvgAggregateOutputType | null
    _sum: PolicyDocumentSumAggregateOutputType | null
    _min: PolicyDocumentMinAggregateOutputType | null
    _max: PolicyDocumentMaxAggregateOutputType | null
  }

  export type PolicyDocumentAvgAggregateOutputType = {
    id: number | null
    policyId: number | null
  }

  export type PolicyDocumentSumAggregateOutputType = {
    id: number | null
    policyId: number | null
  }

  export type PolicyDocumentMinAggregateOutputType = {
    id: number | null
    policyId: number | null
    fileName: string | null
    filePath: string | null
    uploadedAt: Date | null
  }

  export type PolicyDocumentMaxAggregateOutputType = {
    id: number | null
    policyId: number | null
    fileName: string | null
    filePath: string | null
    uploadedAt: Date | null
  }

  export type PolicyDocumentCountAggregateOutputType = {
    id: number
    policyId: number
    fileName: number
    filePath: number
    uploadedAt: number
    _all: number
  }


  export type PolicyDocumentAvgAggregateInputType = {
    id?: true
    policyId?: true
  }

  export type PolicyDocumentSumAggregateInputType = {
    id?: true
    policyId?: true
  }

  export type PolicyDocumentMinAggregateInputType = {
    id?: true
    policyId?: true
    fileName?: true
    filePath?: true
    uploadedAt?: true
  }

  export type PolicyDocumentMaxAggregateInputType = {
    id?: true
    policyId?: true
    fileName?: true
    filePath?: true
    uploadedAt?: true
  }

  export type PolicyDocumentCountAggregateInputType = {
    id?: true
    policyId?: true
    fileName?: true
    filePath?: true
    uploadedAt?: true
    _all?: true
  }

  export type PolicyDocumentAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which PolicyDocument to aggregate.
     */
    where?: PolicyDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PolicyDocuments to fetch.
     */
    orderBy?: PolicyDocumentOrderByWithRelationInput | PolicyDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: PolicyDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PolicyDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PolicyDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned PolicyDocuments
    **/
    _count?: true | PolicyDocumentCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: PolicyDocumentAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: PolicyDocumentSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: PolicyDocumentMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: PolicyDocumentMaxAggregateInputType
  }

  export type GetPolicyDocumentAggregateType<T extends PolicyDocumentAggregateArgs> = {
        [P in keyof T & keyof AggregatePolicyDocument]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregatePolicyDocument[P]>
      : GetScalarType<T[P], AggregatePolicyDocument[P]>
  }




  export type PolicyDocumentGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PolicyDocumentWhereInput
    orderBy?: PolicyDocumentOrderByWithAggregationInput | PolicyDocumentOrderByWithAggregationInput[]
    by: PolicyDocumentScalarFieldEnum[] | PolicyDocumentScalarFieldEnum
    having?: PolicyDocumentScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: PolicyDocumentCountAggregateInputType | true
    _avg?: PolicyDocumentAvgAggregateInputType
    _sum?: PolicyDocumentSumAggregateInputType
    _min?: PolicyDocumentMinAggregateInputType
    _max?: PolicyDocumentMaxAggregateInputType
  }

  export type PolicyDocumentGroupByOutputType = {
    id: number
    policyId: number
    fileName: string
    filePath: string
    uploadedAt: Date
    _count: PolicyDocumentCountAggregateOutputType | null
    _avg: PolicyDocumentAvgAggregateOutputType | null
    _sum: PolicyDocumentSumAggregateOutputType | null
    _min: PolicyDocumentMinAggregateOutputType | null
    _max: PolicyDocumentMaxAggregateOutputType | null
  }

  type GetPolicyDocumentGroupByPayload<T extends PolicyDocumentGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<PolicyDocumentGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof PolicyDocumentGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], PolicyDocumentGroupByOutputType[P]>
            : GetScalarType<T[P], PolicyDocumentGroupByOutputType[P]>
        }
      >
    >


  export type PolicyDocumentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    policyId?: boolean
    fileName?: boolean
    filePath?: boolean
    uploadedAt?: boolean
    policy?: boolean | PolicyDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["policyDocument"]>

  export type PolicyDocumentSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    policyId?: boolean
    fileName?: boolean
    filePath?: boolean
    uploadedAt?: boolean
    policy?: boolean | PolicyDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["policyDocument"]>

  export type PolicyDocumentSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    policyId?: boolean
    fileName?: boolean
    filePath?: boolean
    uploadedAt?: boolean
    policy?: boolean | PolicyDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["policyDocument"]>

  export type PolicyDocumentSelectScalar = {
    id?: boolean
    policyId?: boolean
    fileName?: boolean
    filePath?: boolean
    uploadedAt?: boolean
  }

  export type PolicyDocumentOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "policyId" | "fileName" | "filePath" | "uploadedAt", ExtArgs["result"]["policyDocument"]>
  export type PolicyDocumentInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    policy?: boolean | PolicyDefaultArgs<ExtArgs>
  }
  export type PolicyDocumentIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    policy?: boolean | PolicyDefaultArgs<ExtArgs>
  }
  export type PolicyDocumentIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    policy?: boolean | PolicyDefaultArgs<ExtArgs>
  }

  export type $PolicyDocumentPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "PolicyDocument"
    objects: {
      policy: Prisma.$PolicyPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      policyId: number
      fileName: string
      filePath: string
      uploadedAt: Date
    }, ExtArgs["result"]["policyDocument"]>
    composites: {}
  }

  type PolicyDocumentGetPayload<S extends boolean | null | undefined | PolicyDocumentDefaultArgs> = $Result.GetResult<Prisma.$PolicyDocumentPayload, S>

  type PolicyDocumentCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<PolicyDocumentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: PolicyDocumentCountAggregateInputType | true
    }

  export interface PolicyDocumentDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['PolicyDocument'], meta: { name: 'PolicyDocument' } }
    /**
     * Find zero or one PolicyDocument that matches the filter.
     * @param {PolicyDocumentFindUniqueArgs} args - Arguments to find a PolicyDocument
     * @example
     * // Get one PolicyDocument
     * const policyDocument = await prisma.policyDocument.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends PolicyDocumentFindUniqueArgs>(args: SelectSubset<T, PolicyDocumentFindUniqueArgs<ExtArgs>>): Prisma__PolicyDocumentClient<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one PolicyDocument that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {PolicyDocumentFindUniqueOrThrowArgs} args - Arguments to find a PolicyDocument
     * @example
     * // Get one PolicyDocument
     * const policyDocument = await prisma.policyDocument.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends PolicyDocumentFindUniqueOrThrowArgs>(args: SelectSubset<T, PolicyDocumentFindUniqueOrThrowArgs<ExtArgs>>): Prisma__PolicyDocumentClient<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first PolicyDocument that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyDocumentFindFirstArgs} args - Arguments to find a PolicyDocument
     * @example
     * // Get one PolicyDocument
     * const policyDocument = await prisma.policyDocument.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends PolicyDocumentFindFirstArgs>(args?: SelectSubset<T, PolicyDocumentFindFirstArgs<ExtArgs>>): Prisma__PolicyDocumentClient<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first PolicyDocument that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyDocumentFindFirstOrThrowArgs} args - Arguments to find a PolicyDocument
     * @example
     * // Get one PolicyDocument
     * const policyDocument = await prisma.policyDocument.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends PolicyDocumentFindFirstOrThrowArgs>(args?: SelectSubset<T, PolicyDocumentFindFirstOrThrowArgs<ExtArgs>>): Prisma__PolicyDocumentClient<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more PolicyDocuments that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyDocumentFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all PolicyDocuments
     * const policyDocuments = await prisma.policyDocument.findMany()
     * 
     * // Get first 10 PolicyDocuments
     * const policyDocuments = await prisma.policyDocument.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const policyDocumentWithIdOnly = await prisma.policyDocument.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends PolicyDocumentFindManyArgs>(args?: SelectSubset<T, PolicyDocumentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a PolicyDocument.
     * @param {PolicyDocumentCreateArgs} args - Arguments to create a PolicyDocument.
     * @example
     * // Create one PolicyDocument
     * const PolicyDocument = await prisma.policyDocument.create({
     *   data: {
     *     // ... data to create a PolicyDocument
     *   }
     * })
     * 
     */
    create<T extends PolicyDocumentCreateArgs>(args: SelectSubset<T, PolicyDocumentCreateArgs<ExtArgs>>): Prisma__PolicyDocumentClient<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many PolicyDocuments.
     * @param {PolicyDocumentCreateManyArgs} args - Arguments to create many PolicyDocuments.
     * @example
     * // Create many PolicyDocuments
     * const policyDocument = await prisma.policyDocument.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends PolicyDocumentCreateManyArgs>(args?: SelectSubset<T, PolicyDocumentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many PolicyDocuments and returns the data saved in the database.
     * @param {PolicyDocumentCreateManyAndReturnArgs} args - Arguments to create many PolicyDocuments.
     * @example
     * // Create many PolicyDocuments
     * const policyDocument = await prisma.policyDocument.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many PolicyDocuments and only return the `id`
     * const policyDocumentWithIdOnly = await prisma.policyDocument.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends PolicyDocumentCreateManyAndReturnArgs>(args?: SelectSubset<T, PolicyDocumentCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a PolicyDocument.
     * @param {PolicyDocumentDeleteArgs} args - Arguments to delete one PolicyDocument.
     * @example
     * // Delete one PolicyDocument
     * const PolicyDocument = await prisma.policyDocument.delete({
     *   where: {
     *     // ... filter to delete one PolicyDocument
     *   }
     * })
     * 
     */
    delete<T extends PolicyDocumentDeleteArgs>(args: SelectSubset<T, PolicyDocumentDeleteArgs<ExtArgs>>): Prisma__PolicyDocumentClient<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one PolicyDocument.
     * @param {PolicyDocumentUpdateArgs} args - Arguments to update one PolicyDocument.
     * @example
     * // Update one PolicyDocument
     * const policyDocument = await prisma.policyDocument.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends PolicyDocumentUpdateArgs>(args: SelectSubset<T, PolicyDocumentUpdateArgs<ExtArgs>>): Prisma__PolicyDocumentClient<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more PolicyDocuments.
     * @param {PolicyDocumentDeleteManyArgs} args - Arguments to filter PolicyDocuments to delete.
     * @example
     * // Delete a few PolicyDocuments
     * const { count } = await prisma.policyDocument.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends PolicyDocumentDeleteManyArgs>(args?: SelectSubset<T, PolicyDocumentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more PolicyDocuments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyDocumentUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many PolicyDocuments
     * const policyDocument = await prisma.policyDocument.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends PolicyDocumentUpdateManyArgs>(args: SelectSubset<T, PolicyDocumentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more PolicyDocuments and returns the data updated in the database.
     * @param {PolicyDocumentUpdateManyAndReturnArgs} args - Arguments to update many PolicyDocuments.
     * @example
     * // Update many PolicyDocuments
     * const policyDocument = await prisma.policyDocument.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more PolicyDocuments and only return the `id`
     * const policyDocumentWithIdOnly = await prisma.policyDocument.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends PolicyDocumentUpdateManyAndReturnArgs>(args: SelectSubset<T, PolicyDocumentUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one PolicyDocument.
     * @param {PolicyDocumentUpsertArgs} args - Arguments to update or create a PolicyDocument.
     * @example
     * // Update or create a PolicyDocument
     * const policyDocument = await prisma.policyDocument.upsert({
     *   create: {
     *     // ... data to create a PolicyDocument
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the PolicyDocument we want to update
     *   }
     * })
     */
    upsert<T extends PolicyDocumentUpsertArgs>(args: SelectSubset<T, PolicyDocumentUpsertArgs<ExtArgs>>): Prisma__PolicyDocumentClient<$Result.GetResult<Prisma.$PolicyDocumentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of PolicyDocuments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyDocumentCountArgs} args - Arguments to filter PolicyDocuments to count.
     * @example
     * // Count the number of PolicyDocuments
     * const count = await prisma.policyDocument.count({
     *   where: {
     *     // ... the filter for the PolicyDocuments we want to count
     *   }
     * })
    **/
    count<T extends PolicyDocumentCountArgs>(
      args?: Subset<T, PolicyDocumentCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], PolicyDocumentCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a PolicyDocument.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyDocumentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends PolicyDocumentAggregateArgs>(args: Subset<T, PolicyDocumentAggregateArgs>): Prisma.PrismaPromise<GetPolicyDocumentAggregateType<T>>

    /**
     * Group by PolicyDocument.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PolicyDocumentGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends PolicyDocumentGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: PolicyDocumentGroupByArgs['orderBy'] }
        : { orderBy?: PolicyDocumentGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, PolicyDocumentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPolicyDocumentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the PolicyDocument model
   */
  readonly fields: PolicyDocumentFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for PolicyDocument.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__PolicyDocumentClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    policy<T extends PolicyDefaultArgs<ExtArgs> = {}>(args?: Subset<T, PolicyDefaultArgs<ExtArgs>>): Prisma__PolicyClient<$Result.GetResult<Prisma.$PolicyPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the PolicyDocument model
   */
  interface PolicyDocumentFieldRefs {
    readonly id: FieldRef<"PolicyDocument", 'Int'>
    readonly policyId: FieldRef<"PolicyDocument", 'Int'>
    readonly fileName: FieldRef<"PolicyDocument", 'String'>
    readonly filePath: FieldRef<"PolicyDocument", 'String'>
    readonly uploadedAt: FieldRef<"PolicyDocument", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * PolicyDocument findUnique
   */
  export type PolicyDocumentFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    /**
     * Filter, which PolicyDocument to fetch.
     */
    where: PolicyDocumentWhereUniqueInput
  }

  /**
   * PolicyDocument findUniqueOrThrow
   */
  export type PolicyDocumentFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    /**
     * Filter, which PolicyDocument to fetch.
     */
    where: PolicyDocumentWhereUniqueInput
  }

  /**
   * PolicyDocument findFirst
   */
  export type PolicyDocumentFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    /**
     * Filter, which PolicyDocument to fetch.
     */
    where?: PolicyDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PolicyDocuments to fetch.
     */
    orderBy?: PolicyDocumentOrderByWithRelationInput | PolicyDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for PolicyDocuments.
     */
    cursor?: PolicyDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PolicyDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PolicyDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of PolicyDocuments.
     */
    distinct?: PolicyDocumentScalarFieldEnum | PolicyDocumentScalarFieldEnum[]
  }

  /**
   * PolicyDocument findFirstOrThrow
   */
  export type PolicyDocumentFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    /**
     * Filter, which PolicyDocument to fetch.
     */
    where?: PolicyDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PolicyDocuments to fetch.
     */
    orderBy?: PolicyDocumentOrderByWithRelationInput | PolicyDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for PolicyDocuments.
     */
    cursor?: PolicyDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PolicyDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PolicyDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of PolicyDocuments.
     */
    distinct?: PolicyDocumentScalarFieldEnum | PolicyDocumentScalarFieldEnum[]
  }

  /**
   * PolicyDocument findMany
   */
  export type PolicyDocumentFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    /**
     * Filter, which PolicyDocuments to fetch.
     */
    where?: PolicyDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PolicyDocuments to fetch.
     */
    orderBy?: PolicyDocumentOrderByWithRelationInput | PolicyDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing PolicyDocuments.
     */
    cursor?: PolicyDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PolicyDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PolicyDocuments.
     */
    skip?: number
    distinct?: PolicyDocumentScalarFieldEnum | PolicyDocumentScalarFieldEnum[]
  }

  /**
   * PolicyDocument create
   */
  export type PolicyDocumentCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    /**
     * The data needed to create a PolicyDocument.
     */
    data: XOR<PolicyDocumentCreateInput, PolicyDocumentUncheckedCreateInput>
  }

  /**
   * PolicyDocument createMany
   */
  export type PolicyDocumentCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many PolicyDocuments.
     */
    data: PolicyDocumentCreateManyInput | PolicyDocumentCreateManyInput[]
  }

  /**
   * PolicyDocument createManyAndReturn
   */
  export type PolicyDocumentCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * The data used to create many PolicyDocuments.
     */
    data: PolicyDocumentCreateManyInput | PolicyDocumentCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * PolicyDocument update
   */
  export type PolicyDocumentUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    /**
     * The data needed to update a PolicyDocument.
     */
    data: XOR<PolicyDocumentUpdateInput, PolicyDocumentUncheckedUpdateInput>
    /**
     * Choose, which PolicyDocument to update.
     */
    where: PolicyDocumentWhereUniqueInput
  }

  /**
   * PolicyDocument updateMany
   */
  export type PolicyDocumentUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update PolicyDocuments.
     */
    data: XOR<PolicyDocumentUpdateManyMutationInput, PolicyDocumentUncheckedUpdateManyInput>
    /**
     * Filter which PolicyDocuments to update
     */
    where?: PolicyDocumentWhereInput
    /**
     * Limit how many PolicyDocuments to update.
     */
    limit?: number
  }

  /**
   * PolicyDocument updateManyAndReturn
   */
  export type PolicyDocumentUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * The data used to update PolicyDocuments.
     */
    data: XOR<PolicyDocumentUpdateManyMutationInput, PolicyDocumentUncheckedUpdateManyInput>
    /**
     * Filter which PolicyDocuments to update
     */
    where?: PolicyDocumentWhereInput
    /**
     * Limit how many PolicyDocuments to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * PolicyDocument upsert
   */
  export type PolicyDocumentUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    /**
     * The filter to search for the PolicyDocument to update in case it exists.
     */
    where: PolicyDocumentWhereUniqueInput
    /**
     * In case the PolicyDocument found by the `where` argument doesn't exist, create a new PolicyDocument with this data.
     */
    create: XOR<PolicyDocumentCreateInput, PolicyDocumentUncheckedCreateInput>
    /**
     * In case the PolicyDocument was found with the provided `where` argument, update it with this data.
     */
    update: XOR<PolicyDocumentUpdateInput, PolicyDocumentUncheckedUpdateInput>
  }

  /**
   * PolicyDocument delete
   */
  export type PolicyDocumentDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
    /**
     * Filter which PolicyDocument to delete.
     */
    where: PolicyDocumentWhereUniqueInput
  }

  /**
   * PolicyDocument deleteMany
   */
  export type PolicyDocumentDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which PolicyDocuments to delete
     */
    where?: PolicyDocumentWhereInput
    /**
     * Limit how many PolicyDocuments to delete.
     */
    limit?: number
  }

  /**
   * PolicyDocument without action
   */
  export type PolicyDocumentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PolicyDocument
     */
    select?: PolicyDocumentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PolicyDocument
     */
    omit?: PolicyDocumentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PolicyDocumentInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const EmployeeScalarFieldEnum: {
    id: 'id',
    firstName: 'firstName',
    lastName: 'lastName',
    email: 'email',
    phone: 'phone',
    address: 'address',
    employerName: 'employerName',
    groupId: 'groupId',
    memberId: 'memberId'
  };

  export type EmployeeScalarFieldEnum = (typeof EmployeeScalarFieldEnum)[keyof typeof EmployeeScalarFieldEnum]


  export const ClaimScalarFieldEnum: {
    id: 'id',
    employeeId: 'employeeId',
    claimType: 'claimType',
    description: 'description',
    incidentDate: 'incidentDate',
    dateFiled: 'dateFiled',
    status: 'status'
  };

  export type ClaimScalarFieldEnum = (typeof ClaimScalarFieldEnum)[keyof typeof ClaimScalarFieldEnum]


  export const ClaimDocumentScalarFieldEnum: {
    id: 'id',
    claimId: 'claimId',
    fileName: 'fileName',
    filePath: 'filePath',
    uploadedAt: 'uploadedAt'
  };

  export type ClaimDocumentScalarFieldEnum = (typeof ClaimDocumentScalarFieldEnum)[keyof typeof ClaimDocumentScalarFieldEnum]


  export const ClaimCommentScalarFieldEnum: {
    id: 'id',
    claimId: 'claimId',
    text: 'text',
    createdAt: 'createdAt'
  };

  export type ClaimCommentScalarFieldEnum = (typeof ClaimCommentScalarFieldEnum)[keyof typeof ClaimCommentScalarFieldEnum]


  export const PolicyScalarFieldEnum: {
    id: 'id',
    employeeId: 'employeeId',
    policyOwner: 'policyOwner',
    insured: 'insured',
    spouse: 'spouse',
    group: 'group',
    policyNumber: 'policyNumber',
    originalEffectiveDate: 'originalEffectiveDate',
    scheduledEffectiveDate: 'scheduledEffectiveDate',
    issuedAge: 'issuedAge',
    insuredCoverage: 'insuredCoverage',
    spouseCoverage: 'spouseCoverage'
  };

  export type PolicyScalarFieldEnum = (typeof PolicyScalarFieldEnum)[keyof typeof PolicyScalarFieldEnum]


  export const PolicyDocumentScalarFieldEnum: {
    id: 'id',
    policyId: 'policyId',
    fileName: 'fileName',
    filePath: 'filePath',
    uploadedAt: 'uploadedAt'
  };

  export type PolicyDocumentScalarFieldEnum = (typeof PolicyDocumentScalarFieldEnum)[keyof typeof PolicyDocumentScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    
  /**
   * Deep Input Types
   */


  export type EmployeeWhereInput = {
    AND?: EmployeeWhereInput | EmployeeWhereInput[]
    OR?: EmployeeWhereInput[]
    NOT?: EmployeeWhereInput | EmployeeWhereInput[]
    id?: IntFilter<"Employee"> | number
    firstName?: StringFilter<"Employee"> | string
    lastName?: StringFilter<"Employee"> | string
    email?: StringFilter<"Employee"> | string
    phone?: StringNullableFilter<"Employee"> | string | null
    address?: StringNullableFilter<"Employee"> | string | null
    employerName?: StringNullableFilter<"Employee"> | string | null
    groupId?: StringNullableFilter<"Employee"> | string | null
    memberId?: StringNullableFilter<"Employee"> | string | null
    claims?: ClaimListRelationFilter
    policy?: XOR<PolicyNullableScalarRelationFilter, PolicyWhereInput> | null
  }

  export type EmployeeOrderByWithRelationInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    phone?: SortOrderInput | SortOrder
    address?: SortOrderInput | SortOrder
    employerName?: SortOrderInput | SortOrder
    groupId?: SortOrderInput | SortOrder
    memberId?: SortOrderInput | SortOrder
    claims?: ClaimOrderByRelationAggregateInput
    policy?: PolicyOrderByWithRelationInput
  }

  export type EmployeeWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    email?: string
    AND?: EmployeeWhereInput | EmployeeWhereInput[]
    OR?: EmployeeWhereInput[]
    NOT?: EmployeeWhereInput | EmployeeWhereInput[]
    firstName?: StringFilter<"Employee"> | string
    lastName?: StringFilter<"Employee"> | string
    phone?: StringNullableFilter<"Employee"> | string | null
    address?: StringNullableFilter<"Employee"> | string | null
    employerName?: StringNullableFilter<"Employee"> | string | null
    groupId?: StringNullableFilter<"Employee"> | string | null
    memberId?: StringNullableFilter<"Employee"> | string | null
    claims?: ClaimListRelationFilter
    policy?: XOR<PolicyNullableScalarRelationFilter, PolicyWhereInput> | null
  }, "id" | "email">

  export type EmployeeOrderByWithAggregationInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    phone?: SortOrderInput | SortOrder
    address?: SortOrderInput | SortOrder
    employerName?: SortOrderInput | SortOrder
    groupId?: SortOrderInput | SortOrder
    memberId?: SortOrderInput | SortOrder
    _count?: EmployeeCountOrderByAggregateInput
    _avg?: EmployeeAvgOrderByAggregateInput
    _max?: EmployeeMaxOrderByAggregateInput
    _min?: EmployeeMinOrderByAggregateInput
    _sum?: EmployeeSumOrderByAggregateInput
  }

  export type EmployeeScalarWhereWithAggregatesInput = {
    AND?: EmployeeScalarWhereWithAggregatesInput | EmployeeScalarWhereWithAggregatesInput[]
    OR?: EmployeeScalarWhereWithAggregatesInput[]
    NOT?: EmployeeScalarWhereWithAggregatesInput | EmployeeScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Employee"> | number
    firstName?: StringWithAggregatesFilter<"Employee"> | string
    lastName?: StringWithAggregatesFilter<"Employee"> | string
    email?: StringWithAggregatesFilter<"Employee"> | string
    phone?: StringNullableWithAggregatesFilter<"Employee"> | string | null
    address?: StringNullableWithAggregatesFilter<"Employee"> | string | null
    employerName?: StringNullableWithAggregatesFilter<"Employee"> | string | null
    groupId?: StringNullableWithAggregatesFilter<"Employee"> | string | null
    memberId?: StringNullableWithAggregatesFilter<"Employee"> | string | null
  }

  export type ClaimWhereInput = {
    AND?: ClaimWhereInput | ClaimWhereInput[]
    OR?: ClaimWhereInput[]
    NOT?: ClaimWhereInput | ClaimWhereInput[]
    id?: IntFilter<"Claim"> | number
    employeeId?: IntFilter<"Claim"> | number
    claimType?: StringFilter<"Claim"> | string
    description?: StringFilter<"Claim"> | string
    incidentDate?: DateTimeNullableFilter<"Claim"> | Date | string | null
    dateFiled?: DateTimeFilter<"Claim"> | Date | string
    status?: StringFilter<"Claim"> | string
    employee?: XOR<EmployeeScalarRelationFilter, EmployeeWhereInput>
    documents?: ClaimDocumentListRelationFilter
    comments?: ClaimCommentListRelationFilter
  }

  export type ClaimOrderByWithRelationInput = {
    id?: SortOrder
    employeeId?: SortOrder
    claimType?: SortOrder
    description?: SortOrder
    incidentDate?: SortOrderInput | SortOrder
    dateFiled?: SortOrder
    status?: SortOrder
    employee?: EmployeeOrderByWithRelationInput
    documents?: ClaimDocumentOrderByRelationAggregateInput
    comments?: ClaimCommentOrderByRelationAggregateInput
  }

  export type ClaimWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: ClaimWhereInput | ClaimWhereInput[]
    OR?: ClaimWhereInput[]
    NOT?: ClaimWhereInput | ClaimWhereInput[]
    employeeId?: IntFilter<"Claim"> | number
    claimType?: StringFilter<"Claim"> | string
    description?: StringFilter<"Claim"> | string
    incidentDate?: DateTimeNullableFilter<"Claim"> | Date | string | null
    dateFiled?: DateTimeFilter<"Claim"> | Date | string
    status?: StringFilter<"Claim"> | string
    employee?: XOR<EmployeeScalarRelationFilter, EmployeeWhereInput>
    documents?: ClaimDocumentListRelationFilter
    comments?: ClaimCommentListRelationFilter
  }, "id">

  export type ClaimOrderByWithAggregationInput = {
    id?: SortOrder
    employeeId?: SortOrder
    claimType?: SortOrder
    description?: SortOrder
    incidentDate?: SortOrderInput | SortOrder
    dateFiled?: SortOrder
    status?: SortOrder
    _count?: ClaimCountOrderByAggregateInput
    _avg?: ClaimAvgOrderByAggregateInput
    _max?: ClaimMaxOrderByAggregateInput
    _min?: ClaimMinOrderByAggregateInput
    _sum?: ClaimSumOrderByAggregateInput
  }

  export type ClaimScalarWhereWithAggregatesInput = {
    AND?: ClaimScalarWhereWithAggregatesInput | ClaimScalarWhereWithAggregatesInput[]
    OR?: ClaimScalarWhereWithAggregatesInput[]
    NOT?: ClaimScalarWhereWithAggregatesInput | ClaimScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Claim"> | number
    employeeId?: IntWithAggregatesFilter<"Claim"> | number
    claimType?: StringWithAggregatesFilter<"Claim"> | string
    description?: StringWithAggregatesFilter<"Claim"> | string
    incidentDate?: DateTimeNullableWithAggregatesFilter<"Claim"> | Date | string | null
    dateFiled?: DateTimeWithAggregatesFilter<"Claim"> | Date | string
    status?: StringWithAggregatesFilter<"Claim"> | string
  }

  export type ClaimDocumentWhereInput = {
    AND?: ClaimDocumentWhereInput | ClaimDocumentWhereInput[]
    OR?: ClaimDocumentWhereInput[]
    NOT?: ClaimDocumentWhereInput | ClaimDocumentWhereInput[]
    id?: IntFilter<"ClaimDocument"> | number
    claimId?: IntFilter<"ClaimDocument"> | number
    fileName?: StringFilter<"ClaimDocument"> | string
    filePath?: StringFilter<"ClaimDocument"> | string
    uploadedAt?: DateTimeFilter<"ClaimDocument"> | Date | string
    claim?: XOR<ClaimScalarRelationFilter, ClaimWhereInput>
  }

  export type ClaimDocumentOrderByWithRelationInput = {
    id?: SortOrder
    claimId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
    claim?: ClaimOrderByWithRelationInput
  }

  export type ClaimDocumentWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: ClaimDocumentWhereInput | ClaimDocumentWhereInput[]
    OR?: ClaimDocumentWhereInput[]
    NOT?: ClaimDocumentWhereInput | ClaimDocumentWhereInput[]
    claimId?: IntFilter<"ClaimDocument"> | number
    fileName?: StringFilter<"ClaimDocument"> | string
    filePath?: StringFilter<"ClaimDocument"> | string
    uploadedAt?: DateTimeFilter<"ClaimDocument"> | Date | string
    claim?: XOR<ClaimScalarRelationFilter, ClaimWhereInput>
  }, "id">

  export type ClaimDocumentOrderByWithAggregationInput = {
    id?: SortOrder
    claimId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
    _count?: ClaimDocumentCountOrderByAggregateInput
    _avg?: ClaimDocumentAvgOrderByAggregateInput
    _max?: ClaimDocumentMaxOrderByAggregateInput
    _min?: ClaimDocumentMinOrderByAggregateInput
    _sum?: ClaimDocumentSumOrderByAggregateInput
  }

  export type ClaimDocumentScalarWhereWithAggregatesInput = {
    AND?: ClaimDocumentScalarWhereWithAggregatesInput | ClaimDocumentScalarWhereWithAggregatesInput[]
    OR?: ClaimDocumentScalarWhereWithAggregatesInput[]
    NOT?: ClaimDocumentScalarWhereWithAggregatesInput | ClaimDocumentScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"ClaimDocument"> | number
    claimId?: IntWithAggregatesFilter<"ClaimDocument"> | number
    fileName?: StringWithAggregatesFilter<"ClaimDocument"> | string
    filePath?: StringWithAggregatesFilter<"ClaimDocument"> | string
    uploadedAt?: DateTimeWithAggregatesFilter<"ClaimDocument"> | Date | string
  }

  export type ClaimCommentWhereInput = {
    AND?: ClaimCommentWhereInput | ClaimCommentWhereInput[]
    OR?: ClaimCommentWhereInput[]
    NOT?: ClaimCommentWhereInput | ClaimCommentWhereInput[]
    id?: IntFilter<"ClaimComment"> | number
    claimId?: IntFilter<"ClaimComment"> | number
    text?: StringFilter<"ClaimComment"> | string
    createdAt?: DateTimeFilter<"ClaimComment"> | Date | string
    claim?: XOR<ClaimScalarRelationFilter, ClaimWhereInput>
  }

  export type ClaimCommentOrderByWithRelationInput = {
    id?: SortOrder
    claimId?: SortOrder
    text?: SortOrder
    createdAt?: SortOrder
    claim?: ClaimOrderByWithRelationInput
  }

  export type ClaimCommentWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: ClaimCommentWhereInput | ClaimCommentWhereInput[]
    OR?: ClaimCommentWhereInput[]
    NOT?: ClaimCommentWhereInput | ClaimCommentWhereInput[]
    claimId?: IntFilter<"ClaimComment"> | number
    text?: StringFilter<"ClaimComment"> | string
    createdAt?: DateTimeFilter<"ClaimComment"> | Date | string
    claim?: XOR<ClaimScalarRelationFilter, ClaimWhereInput>
  }, "id">

  export type ClaimCommentOrderByWithAggregationInput = {
    id?: SortOrder
    claimId?: SortOrder
    text?: SortOrder
    createdAt?: SortOrder
    _count?: ClaimCommentCountOrderByAggregateInput
    _avg?: ClaimCommentAvgOrderByAggregateInput
    _max?: ClaimCommentMaxOrderByAggregateInput
    _min?: ClaimCommentMinOrderByAggregateInput
    _sum?: ClaimCommentSumOrderByAggregateInput
  }

  export type ClaimCommentScalarWhereWithAggregatesInput = {
    AND?: ClaimCommentScalarWhereWithAggregatesInput | ClaimCommentScalarWhereWithAggregatesInput[]
    OR?: ClaimCommentScalarWhereWithAggregatesInput[]
    NOT?: ClaimCommentScalarWhereWithAggregatesInput | ClaimCommentScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"ClaimComment"> | number
    claimId?: IntWithAggregatesFilter<"ClaimComment"> | number
    text?: StringWithAggregatesFilter<"ClaimComment"> | string
    createdAt?: DateTimeWithAggregatesFilter<"ClaimComment"> | Date | string
  }

  export type PolicyWhereInput = {
    AND?: PolicyWhereInput | PolicyWhereInput[]
    OR?: PolicyWhereInput[]
    NOT?: PolicyWhereInput | PolicyWhereInput[]
    id?: IntFilter<"Policy"> | number
    employeeId?: IntFilter<"Policy"> | number
    policyOwner?: StringFilter<"Policy"> | string
    insured?: StringFilter<"Policy"> | string
    spouse?: StringNullableFilter<"Policy"> | string | null
    group?: StringFilter<"Policy"> | string
    policyNumber?: StringFilter<"Policy"> | string
    originalEffectiveDate?: DateTimeNullableFilter<"Policy"> | Date | string | null
    scheduledEffectiveDate?: DateTimeNullableFilter<"Policy"> | Date | string | null
    issuedAge?: IntNullableFilter<"Policy"> | number | null
    insuredCoverage?: FloatNullableFilter<"Policy"> | number | null
    spouseCoverage?: FloatNullableFilter<"Policy"> | number | null
    employee?: XOR<EmployeeScalarRelationFilter, EmployeeWhereInput>
    documents?: PolicyDocumentListRelationFilter
  }

  export type PolicyOrderByWithRelationInput = {
    id?: SortOrder
    employeeId?: SortOrder
    policyOwner?: SortOrder
    insured?: SortOrder
    spouse?: SortOrderInput | SortOrder
    group?: SortOrder
    policyNumber?: SortOrder
    originalEffectiveDate?: SortOrderInput | SortOrder
    scheduledEffectiveDate?: SortOrderInput | SortOrder
    issuedAge?: SortOrderInput | SortOrder
    insuredCoverage?: SortOrderInput | SortOrder
    spouseCoverage?: SortOrderInput | SortOrder
    employee?: EmployeeOrderByWithRelationInput
    documents?: PolicyDocumentOrderByRelationAggregateInput
  }

  export type PolicyWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    employeeId?: number
    AND?: PolicyWhereInput | PolicyWhereInput[]
    OR?: PolicyWhereInput[]
    NOT?: PolicyWhereInput | PolicyWhereInput[]
    policyOwner?: StringFilter<"Policy"> | string
    insured?: StringFilter<"Policy"> | string
    spouse?: StringNullableFilter<"Policy"> | string | null
    group?: StringFilter<"Policy"> | string
    policyNumber?: StringFilter<"Policy"> | string
    originalEffectiveDate?: DateTimeNullableFilter<"Policy"> | Date | string | null
    scheduledEffectiveDate?: DateTimeNullableFilter<"Policy"> | Date | string | null
    issuedAge?: IntNullableFilter<"Policy"> | number | null
    insuredCoverage?: FloatNullableFilter<"Policy"> | number | null
    spouseCoverage?: FloatNullableFilter<"Policy"> | number | null
    employee?: XOR<EmployeeScalarRelationFilter, EmployeeWhereInput>
    documents?: PolicyDocumentListRelationFilter
  }, "id" | "employeeId">

  export type PolicyOrderByWithAggregationInput = {
    id?: SortOrder
    employeeId?: SortOrder
    policyOwner?: SortOrder
    insured?: SortOrder
    spouse?: SortOrderInput | SortOrder
    group?: SortOrder
    policyNumber?: SortOrder
    originalEffectiveDate?: SortOrderInput | SortOrder
    scheduledEffectiveDate?: SortOrderInput | SortOrder
    issuedAge?: SortOrderInput | SortOrder
    insuredCoverage?: SortOrderInput | SortOrder
    spouseCoverage?: SortOrderInput | SortOrder
    _count?: PolicyCountOrderByAggregateInput
    _avg?: PolicyAvgOrderByAggregateInput
    _max?: PolicyMaxOrderByAggregateInput
    _min?: PolicyMinOrderByAggregateInput
    _sum?: PolicySumOrderByAggregateInput
  }

  export type PolicyScalarWhereWithAggregatesInput = {
    AND?: PolicyScalarWhereWithAggregatesInput | PolicyScalarWhereWithAggregatesInput[]
    OR?: PolicyScalarWhereWithAggregatesInput[]
    NOT?: PolicyScalarWhereWithAggregatesInput | PolicyScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Policy"> | number
    employeeId?: IntWithAggregatesFilter<"Policy"> | number
    policyOwner?: StringWithAggregatesFilter<"Policy"> | string
    insured?: StringWithAggregatesFilter<"Policy"> | string
    spouse?: StringNullableWithAggregatesFilter<"Policy"> | string | null
    group?: StringWithAggregatesFilter<"Policy"> | string
    policyNumber?: StringWithAggregatesFilter<"Policy"> | string
    originalEffectiveDate?: DateTimeNullableWithAggregatesFilter<"Policy"> | Date | string | null
    scheduledEffectiveDate?: DateTimeNullableWithAggregatesFilter<"Policy"> | Date | string | null
    issuedAge?: IntNullableWithAggregatesFilter<"Policy"> | number | null
    insuredCoverage?: FloatNullableWithAggregatesFilter<"Policy"> | number | null
    spouseCoverage?: FloatNullableWithAggregatesFilter<"Policy"> | number | null
  }

  export type PolicyDocumentWhereInput = {
    AND?: PolicyDocumentWhereInput | PolicyDocumentWhereInput[]
    OR?: PolicyDocumentWhereInput[]
    NOT?: PolicyDocumentWhereInput | PolicyDocumentWhereInput[]
    id?: IntFilter<"PolicyDocument"> | number
    policyId?: IntFilter<"PolicyDocument"> | number
    fileName?: StringFilter<"PolicyDocument"> | string
    filePath?: StringFilter<"PolicyDocument"> | string
    uploadedAt?: DateTimeFilter<"PolicyDocument"> | Date | string
    policy?: XOR<PolicyScalarRelationFilter, PolicyWhereInput>
  }

  export type PolicyDocumentOrderByWithRelationInput = {
    id?: SortOrder
    policyId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
    policy?: PolicyOrderByWithRelationInput
  }

  export type PolicyDocumentWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: PolicyDocumentWhereInput | PolicyDocumentWhereInput[]
    OR?: PolicyDocumentWhereInput[]
    NOT?: PolicyDocumentWhereInput | PolicyDocumentWhereInput[]
    policyId?: IntFilter<"PolicyDocument"> | number
    fileName?: StringFilter<"PolicyDocument"> | string
    filePath?: StringFilter<"PolicyDocument"> | string
    uploadedAt?: DateTimeFilter<"PolicyDocument"> | Date | string
    policy?: XOR<PolicyScalarRelationFilter, PolicyWhereInput>
  }, "id">

  export type PolicyDocumentOrderByWithAggregationInput = {
    id?: SortOrder
    policyId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
    _count?: PolicyDocumentCountOrderByAggregateInput
    _avg?: PolicyDocumentAvgOrderByAggregateInput
    _max?: PolicyDocumentMaxOrderByAggregateInput
    _min?: PolicyDocumentMinOrderByAggregateInput
    _sum?: PolicyDocumentSumOrderByAggregateInput
  }

  export type PolicyDocumentScalarWhereWithAggregatesInput = {
    AND?: PolicyDocumentScalarWhereWithAggregatesInput | PolicyDocumentScalarWhereWithAggregatesInput[]
    OR?: PolicyDocumentScalarWhereWithAggregatesInput[]
    NOT?: PolicyDocumentScalarWhereWithAggregatesInput | PolicyDocumentScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"PolicyDocument"> | number
    policyId?: IntWithAggregatesFilter<"PolicyDocument"> | number
    fileName?: StringWithAggregatesFilter<"PolicyDocument"> | string
    filePath?: StringWithAggregatesFilter<"PolicyDocument"> | string
    uploadedAt?: DateTimeWithAggregatesFilter<"PolicyDocument"> | Date | string
  }

  export type EmployeeCreateInput = {
    firstName: string
    lastName: string
    email: string
    phone?: string | null
    address?: string | null
    employerName?: string | null
    groupId?: string | null
    memberId?: string | null
    claims?: ClaimCreateNestedManyWithoutEmployeeInput
    policy?: PolicyCreateNestedOneWithoutEmployeeInput
  }

  export type EmployeeUncheckedCreateInput = {
    id?: number
    firstName: string
    lastName: string
    email: string
    phone?: string | null
    address?: string | null
    employerName?: string | null
    groupId?: string | null
    memberId?: string | null
    claims?: ClaimUncheckedCreateNestedManyWithoutEmployeeInput
    policy?: PolicyUncheckedCreateNestedOneWithoutEmployeeInput
  }

  export type EmployeeUpdateInput = {
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    employerName?: NullableStringFieldUpdateOperationsInput | string | null
    groupId?: NullableStringFieldUpdateOperationsInput | string | null
    memberId?: NullableStringFieldUpdateOperationsInput | string | null
    claims?: ClaimUpdateManyWithoutEmployeeNestedInput
    policy?: PolicyUpdateOneWithoutEmployeeNestedInput
  }

  export type EmployeeUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    employerName?: NullableStringFieldUpdateOperationsInput | string | null
    groupId?: NullableStringFieldUpdateOperationsInput | string | null
    memberId?: NullableStringFieldUpdateOperationsInput | string | null
    claims?: ClaimUncheckedUpdateManyWithoutEmployeeNestedInput
    policy?: PolicyUncheckedUpdateOneWithoutEmployeeNestedInput
  }

  export type EmployeeCreateManyInput = {
    id?: number
    firstName: string
    lastName: string
    email: string
    phone?: string | null
    address?: string | null
    employerName?: string | null
    groupId?: string | null
    memberId?: string | null
  }

  export type EmployeeUpdateManyMutationInput = {
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    employerName?: NullableStringFieldUpdateOperationsInput | string | null
    groupId?: NullableStringFieldUpdateOperationsInput | string | null
    memberId?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type EmployeeUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    employerName?: NullableStringFieldUpdateOperationsInput | string | null
    groupId?: NullableStringFieldUpdateOperationsInput | string | null
    memberId?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type ClaimCreateInput = {
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
    employee: EmployeeCreateNestedOneWithoutClaimsInput
    documents?: ClaimDocumentCreateNestedManyWithoutClaimInput
    comments?: ClaimCommentCreateNestedManyWithoutClaimInput
  }

  export type ClaimUncheckedCreateInput = {
    id?: number
    employeeId: number
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
    documents?: ClaimDocumentUncheckedCreateNestedManyWithoutClaimInput
    comments?: ClaimCommentUncheckedCreateNestedManyWithoutClaimInput
  }

  export type ClaimUpdateInput = {
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
    employee?: EmployeeUpdateOneRequiredWithoutClaimsNestedInput
    documents?: ClaimDocumentUpdateManyWithoutClaimNestedInput
    comments?: ClaimCommentUpdateManyWithoutClaimNestedInput
  }

  export type ClaimUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    employeeId?: IntFieldUpdateOperationsInput | number
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
    documents?: ClaimDocumentUncheckedUpdateManyWithoutClaimNestedInput
    comments?: ClaimCommentUncheckedUpdateManyWithoutClaimNestedInput
  }

  export type ClaimCreateManyInput = {
    id?: number
    employeeId: number
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
  }

  export type ClaimUpdateManyMutationInput = {
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
  }

  export type ClaimUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    employeeId?: IntFieldUpdateOperationsInput | number
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
  }

  export type ClaimDocumentCreateInput = {
    fileName: string
    filePath: string
    uploadedAt?: Date | string
    claim: ClaimCreateNestedOneWithoutDocumentsInput
  }

  export type ClaimDocumentUncheckedCreateInput = {
    id?: number
    claimId: number
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type ClaimDocumentUpdateInput = {
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    claim?: ClaimUpdateOneRequiredWithoutDocumentsNestedInput
  }

  export type ClaimDocumentUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    claimId?: IntFieldUpdateOperationsInput | number
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimDocumentCreateManyInput = {
    id?: number
    claimId: number
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type ClaimDocumentUpdateManyMutationInput = {
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimDocumentUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    claimId?: IntFieldUpdateOperationsInput | number
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimCommentCreateInput = {
    text: string
    createdAt?: Date | string
    claim: ClaimCreateNestedOneWithoutCommentsInput
  }

  export type ClaimCommentUncheckedCreateInput = {
    id?: number
    claimId: number
    text: string
    createdAt?: Date | string
  }

  export type ClaimCommentUpdateInput = {
    text?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    claim?: ClaimUpdateOneRequiredWithoutCommentsNestedInput
  }

  export type ClaimCommentUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    claimId?: IntFieldUpdateOperationsInput | number
    text?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimCommentCreateManyInput = {
    id?: number
    claimId: number
    text: string
    createdAt?: Date | string
  }

  export type ClaimCommentUpdateManyMutationInput = {
    text?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimCommentUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    claimId?: IntFieldUpdateOperationsInput | number
    text?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PolicyCreateInput = {
    policyOwner: string
    insured: string
    spouse?: string | null
    group: string
    policyNumber: string
    originalEffectiveDate?: Date | string | null
    scheduledEffectiveDate?: Date | string | null
    issuedAge?: number | null
    insuredCoverage?: number | null
    spouseCoverage?: number | null
    employee: EmployeeCreateNestedOneWithoutPolicyInput
    documents?: PolicyDocumentCreateNestedManyWithoutPolicyInput
  }

  export type PolicyUncheckedCreateInput = {
    id?: number
    employeeId: number
    policyOwner: string
    insured: string
    spouse?: string | null
    group: string
    policyNumber: string
    originalEffectiveDate?: Date | string | null
    scheduledEffectiveDate?: Date | string | null
    issuedAge?: number | null
    insuredCoverage?: number | null
    spouseCoverage?: number | null
    documents?: PolicyDocumentUncheckedCreateNestedManyWithoutPolicyInput
  }

  export type PolicyUpdateInput = {
    policyOwner?: StringFieldUpdateOperationsInput | string
    insured?: StringFieldUpdateOperationsInput | string
    spouse?: NullableStringFieldUpdateOperationsInput | string | null
    group?: StringFieldUpdateOperationsInput | string
    policyNumber?: StringFieldUpdateOperationsInput | string
    originalEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scheduledEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    issuedAge?: NullableIntFieldUpdateOperationsInput | number | null
    insuredCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    spouseCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    employee?: EmployeeUpdateOneRequiredWithoutPolicyNestedInput
    documents?: PolicyDocumentUpdateManyWithoutPolicyNestedInput
  }

  export type PolicyUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    employeeId?: IntFieldUpdateOperationsInput | number
    policyOwner?: StringFieldUpdateOperationsInput | string
    insured?: StringFieldUpdateOperationsInput | string
    spouse?: NullableStringFieldUpdateOperationsInput | string | null
    group?: StringFieldUpdateOperationsInput | string
    policyNumber?: StringFieldUpdateOperationsInput | string
    originalEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scheduledEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    issuedAge?: NullableIntFieldUpdateOperationsInput | number | null
    insuredCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    spouseCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    documents?: PolicyDocumentUncheckedUpdateManyWithoutPolicyNestedInput
  }

  export type PolicyCreateManyInput = {
    id?: number
    employeeId: number
    policyOwner: string
    insured: string
    spouse?: string | null
    group: string
    policyNumber: string
    originalEffectiveDate?: Date | string | null
    scheduledEffectiveDate?: Date | string | null
    issuedAge?: number | null
    insuredCoverage?: number | null
    spouseCoverage?: number | null
  }

  export type PolicyUpdateManyMutationInput = {
    policyOwner?: StringFieldUpdateOperationsInput | string
    insured?: StringFieldUpdateOperationsInput | string
    spouse?: NullableStringFieldUpdateOperationsInput | string | null
    group?: StringFieldUpdateOperationsInput | string
    policyNumber?: StringFieldUpdateOperationsInput | string
    originalEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scheduledEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    issuedAge?: NullableIntFieldUpdateOperationsInput | number | null
    insuredCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    spouseCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
  }

  export type PolicyUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    employeeId?: IntFieldUpdateOperationsInput | number
    policyOwner?: StringFieldUpdateOperationsInput | string
    insured?: StringFieldUpdateOperationsInput | string
    spouse?: NullableStringFieldUpdateOperationsInput | string | null
    group?: StringFieldUpdateOperationsInput | string
    policyNumber?: StringFieldUpdateOperationsInput | string
    originalEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scheduledEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    issuedAge?: NullableIntFieldUpdateOperationsInput | number | null
    insuredCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    spouseCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
  }

  export type PolicyDocumentCreateInput = {
    fileName: string
    filePath: string
    uploadedAt?: Date | string
    policy: PolicyCreateNestedOneWithoutDocumentsInput
  }

  export type PolicyDocumentUncheckedCreateInput = {
    id?: number
    policyId: number
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type PolicyDocumentUpdateInput = {
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    policy?: PolicyUpdateOneRequiredWithoutDocumentsNestedInput
  }

  export type PolicyDocumentUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    policyId?: IntFieldUpdateOperationsInput | number
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PolicyDocumentCreateManyInput = {
    id?: number
    policyId: number
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type PolicyDocumentUpdateManyMutationInput = {
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PolicyDocumentUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    policyId?: IntFieldUpdateOperationsInput | number
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type ClaimListRelationFilter = {
    every?: ClaimWhereInput
    some?: ClaimWhereInput
    none?: ClaimWhereInput
  }

  export type PolicyNullableScalarRelationFilter = {
    is?: PolicyWhereInput | null
    isNot?: PolicyWhereInput | null
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type ClaimOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type EmployeeCountOrderByAggregateInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    address?: SortOrder
    employerName?: SortOrder
    groupId?: SortOrder
    memberId?: SortOrder
  }

  export type EmployeeAvgOrderByAggregateInput = {
    id?: SortOrder
  }

  export type EmployeeMaxOrderByAggregateInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    address?: SortOrder
    employerName?: SortOrder
    groupId?: SortOrder
    memberId?: SortOrder
  }

  export type EmployeeMinOrderByAggregateInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    address?: SortOrder
    employerName?: SortOrder
    groupId?: SortOrder
    memberId?: SortOrder
  }

  export type EmployeeSumOrderByAggregateInput = {
    id?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type EmployeeScalarRelationFilter = {
    is?: EmployeeWhereInput
    isNot?: EmployeeWhereInput
  }

  export type ClaimDocumentListRelationFilter = {
    every?: ClaimDocumentWhereInput
    some?: ClaimDocumentWhereInput
    none?: ClaimDocumentWhereInput
  }

  export type ClaimCommentListRelationFilter = {
    every?: ClaimCommentWhereInput
    some?: ClaimCommentWhereInput
    none?: ClaimCommentWhereInput
  }

  export type ClaimDocumentOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type ClaimCommentOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type ClaimCountOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
    claimType?: SortOrder
    description?: SortOrder
    incidentDate?: SortOrder
    dateFiled?: SortOrder
    status?: SortOrder
  }

  export type ClaimAvgOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
  }

  export type ClaimMaxOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
    claimType?: SortOrder
    description?: SortOrder
    incidentDate?: SortOrder
    dateFiled?: SortOrder
    status?: SortOrder
  }

  export type ClaimMinOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
    claimType?: SortOrder
    description?: SortOrder
    incidentDate?: SortOrder
    dateFiled?: SortOrder
    status?: SortOrder
  }

  export type ClaimSumOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type ClaimScalarRelationFilter = {
    is?: ClaimWhereInput
    isNot?: ClaimWhereInput
  }

  export type ClaimDocumentCountOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
  }

  export type ClaimDocumentAvgOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
  }

  export type ClaimDocumentMaxOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
  }

  export type ClaimDocumentMinOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
  }

  export type ClaimDocumentSumOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
  }

  export type ClaimCommentCountOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
    text?: SortOrder
    createdAt?: SortOrder
  }

  export type ClaimCommentAvgOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
  }

  export type ClaimCommentMaxOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
    text?: SortOrder
    createdAt?: SortOrder
  }

  export type ClaimCommentMinOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
    text?: SortOrder
    createdAt?: SortOrder
  }

  export type ClaimCommentSumOrderByAggregateInput = {
    id?: SortOrder
    claimId?: SortOrder
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type FloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type PolicyDocumentListRelationFilter = {
    every?: PolicyDocumentWhereInput
    some?: PolicyDocumentWhereInput
    none?: PolicyDocumentWhereInput
  }

  export type PolicyDocumentOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type PolicyCountOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
    policyOwner?: SortOrder
    insured?: SortOrder
    spouse?: SortOrder
    group?: SortOrder
    policyNumber?: SortOrder
    originalEffectiveDate?: SortOrder
    scheduledEffectiveDate?: SortOrder
    issuedAge?: SortOrder
    insuredCoverage?: SortOrder
    spouseCoverage?: SortOrder
  }

  export type PolicyAvgOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
    issuedAge?: SortOrder
    insuredCoverage?: SortOrder
    spouseCoverage?: SortOrder
  }

  export type PolicyMaxOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
    policyOwner?: SortOrder
    insured?: SortOrder
    spouse?: SortOrder
    group?: SortOrder
    policyNumber?: SortOrder
    originalEffectiveDate?: SortOrder
    scheduledEffectiveDate?: SortOrder
    issuedAge?: SortOrder
    insuredCoverage?: SortOrder
    spouseCoverage?: SortOrder
  }

  export type PolicyMinOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
    policyOwner?: SortOrder
    insured?: SortOrder
    spouse?: SortOrder
    group?: SortOrder
    policyNumber?: SortOrder
    originalEffectiveDate?: SortOrder
    scheduledEffectiveDate?: SortOrder
    issuedAge?: SortOrder
    insuredCoverage?: SortOrder
    spouseCoverage?: SortOrder
  }

  export type PolicySumOrderByAggregateInput = {
    id?: SortOrder
    employeeId?: SortOrder
    issuedAge?: SortOrder
    insuredCoverage?: SortOrder
    spouseCoverage?: SortOrder
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type FloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type PolicyScalarRelationFilter = {
    is?: PolicyWhereInput
    isNot?: PolicyWhereInput
  }

  export type PolicyDocumentCountOrderByAggregateInput = {
    id?: SortOrder
    policyId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
  }

  export type PolicyDocumentAvgOrderByAggregateInput = {
    id?: SortOrder
    policyId?: SortOrder
  }

  export type PolicyDocumentMaxOrderByAggregateInput = {
    id?: SortOrder
    policyId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
  }

  export type PolicyDocumentMinOrderByAggregateInput = {
    id?: SortOrder
    policyId?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    uploadedAt?: SortOrder
  }

  export type PolicyDocumentSumOrderByAggregateInput = {
    id?: SortOrder
    policyId?: SortOrder
  }

  export type ClaimCreateNestedManyWithoutEmployeeInput = {
    create?: XOR<ClaimCreateWithoutEmployeeInput, ClaimUncheckedCreateWithoutEmployeeInput> | ClaimCreateWithoutEmployeeInput[] | ClaimUncheckedCreateWithoutEmployeeInput[]
    connectOrCreate?: ClaimCreateOrConnectWithoutEmployeeInput | ClaimCreateOrConnectWithoutEmployeeInput[]
    createMany?: ClaimCreateManyEmployeeInputEnvelope
    connect?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
  }

  export type PolicyCreateNestedOneWithoutEmployeeInput = {
    create?: XOR<PolicyCreateWithoutEmployeeInput, PolicyUncheckedCreateWithoutEmployeeInput>
    connectOrCreate?: PolicyCreateOrConnectWithoutEmployeeInput
    connect?: PolicyWhereUniqueInput
  }

  export type ClaimUncheckedCreateNestedManyWithoutEmployeeInput = {
    create?: XOR<ClaimCreateWithoutEmployeeInput, ClaimUncheckedCreateWithoutEmployeeInput> | ClaimCreateWithoutEmployeeInput[] | ClaimUncheckedCreateWithoutEmployeeInput[]
    connectOrCreate?: ClaimCreateOrConnectWithoutEmployeeInput | ClaimCreateOrConnectWithoutEmployeeInput[]
    createMany?: ClaimCreateManyEmployeeInputEnvelope
    connect?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
  }

  export type PolicyUncheckedCreateNestedOneWithoutEmployeeInput = {
    create?: XOR<PolicyCreateWithoutEmployeeInput, PolicyUncheckedCreateWithoutEmployeeInput>
    connectOrCreate?: PolicyCreateOrConnectWithoutEmployeeInput
    connect?: PolicyWhereUniqueInput
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type ClaimUpdateManyWithoutEmployeeNestedInput = {
    create?: XOR<ClaimCreateWithoutEmployeeInput, ClaimUncheckedCreateWithoutEmployeeInput> | ClaimCreateWithoutEmployeeInput[] | ClaimUncheckedCreateWithoutEmployeeInput[]
    connectOrCreate?: ClaimCreateOrConnectWithoutEmployeeInput | ClaimCreateOrConnectWithoutEmployeeInput[]
    upsert?: ClaimUpsertWithWhereUniqueWithoutEmployeeInput | ClaimUpsertWithWhereUniqueWithoutEmployeeInput[]
    createMany?: ClaimCreateManyEmployeeInputEnvelope
    set?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
    disconnect?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
    delete?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
    connect?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
    update?: ClaimUpdateWithWhereUniqueWithoutEmployeeInput | ClaimUpdateWithWhereUniqueWithoutEmployeeInput[]
    updateMany?: ClaimUpdateManyWithWhereWithoutEmployeeInput | ClaimUpdateManyWithWhereWithoutEmployeeInput[]
    deleteMany?: ClaimScalarWhereInput | ClaimScalarWhereInput[]
  }

  export type PolicyUpdateOneWithoutEmployeeNestedInput = {
    create?: XOR<PolicyCreateWithoutEmployeeInput, PolicyUncheckedCreateWithoutEmployeeInput>
    connectOrCreate?: PolicyCreateOrConnectWithoutEmployeeInput
    upsert?: PolicyUpsertWithoutEmployeeInput
    disconnect?: PolicyWhereInput | boolean
    delete?: PolicyWhereInput | boolean
    connect?: PolicyWhereUniqueInput
    update?: XOR<XOR<PolicyUpdateToOneWithWhereWithoutEmployeeInput, PolicyUpdateWithoutEmployeeInput>, PolicyUncheckedUpdateWithoutEmployeeInput>
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type ClaimUncheckedUpdateManyWithoutEmployeeNestedInput = {
    create?: XOR<ClaimCreateWithoutEmployeeInput, ClaimUncheckedCreateWithoutEmployeeInput> | ClaimCreateWithoutEmployeeInput[] | ClaimUncheckedCreateWithoutEmployeeInput[]
    connectOrCreate?: ClaimCreateOrConnectWithoutEmployeeInput | ClaimCreateOrConnectWithoutEmployeeInput[]
    upsert?: ClaimUpsertWithWhereUniqueWithoutEmployeeInput | ClaimUpsertWithWhereUniqueWithoutEmployeeInput[]
    createMany?: ClaimCreateManyEmployeeInputEnvelope
    set?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
    disconnect?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
    delete?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
    connect?: ClaimWhereUniqueInput | ClaimWhereUniqueInput[]
    update?: ClaimUpdateWithWhereUniqueWithoutEmployeeInput | ClaimUpdateWithWhereUniqueWithoutEmployeeInput[]
    updateMany?: ClaimUpdateManyWithWhereWithoutEmployeeInput | ClaimUpdateManyWithWhereWithoutEmployeeInput[]
    deleteMany?: ClaimScalarWhereInput | ClaimScalarWhereInput[]
  }

  export type PolicyUncheckedUpdateOneWithoutEmployeeNestedInput = {
    create?: XOR<PolicyCreateWithoutEmployeeInput, PolicyUncheckedCreateWithoutEmployeeInput>
    connectOrCreate?: PolicyCreateOrConnectWithoutEmployeeInput
    upsert?: PolicyUpsertWithoutEmployeeInput
    disconnect?: PolicyWhereInput | boolean
    delete?: PolicyWhereInput | boolean
    connect?: PolicyWhereUniqueInput
    update?: XOR<XOR<PolicyUpdateToOneWithWhereWithoutEmployeeInput, PolicyUpdateWithoutEmployeeInput>, PolicyUncheckedUpdateWithoutEmployeeInput>
  }

  export type EmployeeCreateNestedOneWithoutClaimsInput = {
    create?: XOR<EmployeeCreateWithoutClaimsInput, EmployeeUncheckedCreateWithoutClaimsInput>
    connectOrCreate?: EmployeeCreateOrConnectWithoutClaimsInput
    connect?: EmployeeWhereUniqueInput
  }

  export type ClaimDocumentCreateNestedManyWithoutClaimInput = {
    create?: XOR<ClaimDocumentCreateWithoutClaimInput, ClaimDocumentUncheckedCreateWithoutClaimInput> | ClaimDocumentCreateWithoutClaimInput[] | ClaimDocumentUncheckedCreateWithoutClaimInput[]
    connectOrCreate?: ClaimDocumentCreateOrConnectWithoutClaimInput | ClaimDocumentCreateOrConnectWithoutClaimInput[]
    createMany?: ClaimDocumentCreateManyClaimInputEnvelope
    connect?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
  }

  export type ClaimCommentCreateNestedManyWithoutClaimInput = {
    create?: XOR<ClaimCommentCreateWithoutClaimInput, ClaimCommentUncheckedCreateWithoutClaimInput> | ClaimCommentCreateWithoutClaimInput[] | ClaimCommentUncheckedCreateWithoutClaimInput[]
    connectOrCreate?: ClaimCommentCreateOrConnectWithoutClaimInput | ClaimCommentCreateOrConnectWithoutClaimInput[]
    createMany?: ClaimCommentCreateManyClaimInputEnvelope
    connect?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
  }

  export type ClaimDocumentUncheckedCreateNestedManyWithoutClaimInput = {
    create?: XOR<ClaimDocumentCreateWithoutClaimInput, ClaimDocumentUncheckedCreateWithoutClaimInput> | ClaimDocumentCreateWithoutClaimInput[] | ClaimDocumentUncheckedCreateWithoutClaimInput[]
    connectOrCreate?: ClaimDocumentCreateOrConnectWithoutClaimInput | ClaimDocumentCreateOrConnectWithoutClaimInput[]
    createMany?: ClaimDocumentCreateManyClaimInputEnvelope
    connect?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
  }

  export type ClaimCommentUncheckedCreateNestedManyWithoutClaimInput = {
    create?: XOR<ClaimCommentCreateWithoutClaimInput, ClaimCommentUncheckedCreateWithoutClaimInput> | ClaimCommentCreateWithoutClaimInput[] | ClaimCommentUncheckedCreateWithoutClaimInput[]
    connectOrCreate?: ClaimCommentCreateOrConnectWithoutClaimInput | ClaimCommentCreateOrConnectWithoutClaimInput[]
    createMany?: ClaimCommentCreateManyClaimInputEnvelope
    connect?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type EmployeeUpdateOneRequiredWithoutClaimsNestedInput = {
    create?: XOR<EmployeeCreateWithoutClaimsInput, EmployeeUncheckedCreateWithoutClaimsInput>
    connectOrCreate?: EmployeeCreateOrConnectWithoutClaimsInput
    upsert?: EmployeeUpsertWithoutClaimsInput
    connect?: EmployeeWhereUniqueInput
    update?: XOR<XOR<EmployeeUpdateToOneWithWhereWithoutClaimsInput, EmployeeUpdateWithoutClaimsInput>, EmployeeUncheckedUpdateWithoutClaimsInput>
  }

  export type ClaimDocumentUpdateManyWithoutClaimNestedInput = {
    create?: XOR<ClaimDocumentCreateWithoutClaimInput, ClaimDocumentUncheckedCreateWithoutClaimInput> | ClaimDocumentCreateWithoutClaimInput[] | ClaimDocumentUncheckedCreateWithoutClaimInput[]
    connectOrCreate?: ClaimDocumentCreateOrConnectWithoutClaimInput | ClaimDocumentCreateOrConnectWithoutClaimInput[]
    upsert?: ClaimDocumentUpsertWithWhereUniqueWithoutClaimInput | ClaimDocumentUpsertWithWhereUniqueWithoutClaimInput[]
    createMany?: ClaimDocumentCreateManyClaimInputEnvelope
    set?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
    disconnect?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
    delete?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
    connect?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
    update?: ClaimDocumentUpdateWithWhereUniqueWithoutClaimInput | ClaimDocumentUpdateWithWhereUniqueWithoutClaimInput[]
    updateMany?: ClaimDocumentUpdateManyWithWhereWithoutClaimInput | ClaimDocumentUpdateManyWithWhereWithoutClaimInput[]
    deleteMany?: ClaimDocumentScalarWhereInput | ClaimDocumentScalarWhereInput[]
  }

  export type ClaimCommentUpdateManyWithoutClaimNestedInput = {
    create?: XOR<ClaimCommentCreateWithoutClaimInput, ClaimCommentUncheckedCreateWithoutClaimInput> | ClaimCommentCreateWithoutClaimInput[] | ClaimCommentUncheckedCreateWithoutClaimInput[]
    connectOrCreate?: ClaimCommentCreateOrConnectWithoutClaimInput | ClaimCommentCreateOrConnectWithoutClaimInput[]
    upsert?: ClaimCommentUpsertWithWhereUniqueWithoutClaimInput | ClaimCommentUpsertWithWhereUniqueWithoutClaimInput[]
    createMany?: ClaimCommentCreateManyClaimInputEnvelope
    set?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
    disconnect?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
    delete?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
    connect?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
    update?: ClaimCommentUpdateWithWhereUniqueWithoutClaimInput | ClaimCommentUpdateWithWhereUniqueWithoutClaimInput[]
    updateMany?: ClaimCommentUpdateManyWithWhereWithoutClaimInput | ClaimCommentUpdateManyWithWhereWithoutClaimInput[]
    deleteMany?: ClaimCommentScalarWhereInput | ClaimCommentScalarWhereInput[]
  }

  export type ClaimDocumentUncheckedUpdateManyWithoutClaimNestedInput = {
    create?: XOR<ClaimDocumentCreateWithoutClaimInput, ClaimDocumentUncheckedCreateWithoutClaimInput> | ClaimDocumentCreateWithoutClaimInput[] | ClaimDocumentUncheckedCreateWithoutClaimInput[]
    connectOrCreate?: ClaimDocumentCreateOrConnectWithoutClaimInput | ClaimDocumentCreateOrConnectWithoutClaimInput[]
    upsert?: ClaimDocumentUpsertWithWhereUniqueWithoutClaimInput | ClaimDocumentUpsertWithWhereUniqueWithoutClaimInput[]
    createMany?: ClaimDocumentCreateManyClaimInputEnvelope
    set?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
    disconnect?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
    delete?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
    connect?: ClaimDocumentWhereUniqueInput | ClaimDocumentWhereUniqueInput[]
    update?: ClaimDocumentUpdateWithWhereUniqueWithoutClaimInput | ClaimDocumentUpdateWithWhereUniqueWithoutClaimInput[]
    updateMany?: ClaimDocumentUpdateManyWithWhereWithoutClaimInput | ClaimDocumentUpdateManyWithWhereWithoutClaimInput[]
    deleteMany?: ClaimDocumentScalarWhereInput | ClaimDocumentScalarWhereInput[]
  }

  export type ClaimCommentUncheckedUpdateManyWithoutClaimNestedInput = {
    create?: XOR<ClaimCommentCreateWithoutClaimInput, ClaimCommentUncheckedCreateWithoutClaimInput> | ClaimCommentCreateWithoutClaimInput[] | ClaimCommentUncheckedCreateWithoutClaimInput[]
    connectOrCreate?: ClaimCommentCreateOrConnectWithoutClaimInput | ClaimCommentCreateOrConnectWithoutClaimInput[]
    upsert?: ClaimCommentUpsertWithWhereUniqueWithoutClaimInput | ClaimCommentUpsertWithWhereUniqueWithoutClaimInput[]
    createMany?: ClaimCommentCreateManyClaimInputEnvelope
    set?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
    disconnect?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
    delete?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
    connect?: ClaimCommentWhereUniqueInput | ClaimCommentWhereUniqueInput[]
    update?: ClaimCommentUpdateWithWhereUniqueWithoutClaimInput | ClaimCommentUpdateWithWhereUniqueWithoutClaimInput[]
    updateMany?: ClaimCommentUpdateManyWithWhereWithoutClaimInput | ClaimCommentUpdateManyWithWhereWithoutClaimInput[]
    deleteMany?: ClaimCommentScalarWhereInput | ClaimCommentScalarWhereInput[]
  }

  export type ClaimCreateNestedOneWithoutDocumentsInput = {
    create?: XOR<ClaimCreateWithoutDocumentsInput, ClaimUncheckedCreateWithoutDocumentsInput>
    connectOrCreate?: ClaimCreateOrConnectWithoutDocumentsInput
    connect?: ClaimWhereUniqueInput
  }

  export type ClaimUpdateOneRequiredWithoutDocumentsNestedInput = {
    create?: XOR<ClaimCreateWithoutDocumentsInput, ClaimUncheckedCreateWithoutDocumentsInput>
    connectOrCreate?: ClaimCreateOrConnectWithoutDocumentsInput
    upsert?: ClaimUpsertWithoutDocumentsInput
    connect?: ClaimWhereUniqueInput
    update?: XOR<XOR<ClaimUpdateToOneWithWhereWithoutDocumentsInput, ClaimUpdateWithoutDocumentsInput>, ClaimUncheckedUpdateWithoutDocumentsInput>
  }

  export type ClaimCreateNestedOneWithoutCommentsInput = {
    create?: XOR<ClaimCreateWithoutCommentsInput, ClaimUncheckedCreateWithoutCommentsInput>
    connectOrCreate?: ClaimCreateOrConnectWithoutCommentsInput
    connect?: ClaimWhereUniqueInput
  }

  export type ClaimUpdateOneRequiredWithoutCommentsNestedInput = {
    create?: XOR<ClaimCreateWithoutCommentsInput, ClaimUncheckedCreateWithoutCommentsInput>
    connectOrCreate?: ClaimCreateOrConnectWithoutCommentsInput
    upsert?: ClaimUpsertWithoutCommentsInput
    connect?: ClaimWhereUniqueInput
    update?: XOR<XOR<ClaimUpdateToOneWithWhereWithoutCommentsInput, ClaimUpdateWithoutCommentsInput>, ClaimUncheckedUpdateWithoutCommentsInput>
  }

  export type EmployeeCreateNestedOneWithoutPolicyInput = {
    create?: XOR<EmployeeCreateWithoutPolicyInput, EmployeeUncheckedCreateWithoutPolicyInput>
    connectOrCreate?: EmployeeCreateOrConnectWithoutPolicyInput
    connect?: EmployeeWhereUniqueInput
  }

  export type PolicyDocumentCreateNestedManyWithoutPolicyInput = {
    create?: XOR<PolicyDocumentCreateWithoutPolicyInput, PolicyDocumentUncheckedCreateWithoutPolicyInput> | PolicyDocumentCreateWithoutPolicyInput[] | PolicyDocumentUncheckedCreateWithoutPolicyInput[]
    connectOrCreate?: PolicyDocumentCreateOrConnectWithoutPolicyInput | PolicyDocumentCreateOrConnectWithoutPolicyInput[]
    createMany?: PolicyDocumentCreateManyPolicyInputEnvelope
    connect?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
  }

  export type PolicyDocumentUncheckedCreateNestedManyWithoutPolicyInput = {
    create?: XOR<PolicyDocumentCreateWithoutPolicyInput, PolicyDocumentUncheckedCreateWithoutPolicyInput> | PolicyDocumentCreateWithoutPolicyInput[] | PolicyDocumentUncheckedCreateWithoutPolicyInput[]
    connectOrCreate?: PolicyDocumentCreateOrConnectWithoutPolicyInput | PolicyDocumentCreateOrConnectWithoutPolicyInput[]
    createMany?: PolicyDocumentCreateManyPolicyInputEnvelope
    connect?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NullableFloatFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type EmployeeUpdateOneRequiredWithoutPolicyNestedInput = {
    create?: XOR<EmployeeCreateWithoutPolicyInput, EmployeeUncheckedCreateWithoutPolicyInput>
    connectOrCreate?: EmployeeCreateOrConnectWithoutPolicyInput
    upsert?: EmployeeUpsertWithoutPolicyInput
    connect?: EmployeeWhereUniqueInput
    update?: XOR<XOR<EmployeeUpdateToOneWithWhereWithoutPolicyInput, EmployeeUpdateWithoutPolicyInput>, EmployeeUncheckedUpdateWithoutPolicyInput>
  }

  export type PolicyDocumentUpdateManyWithoutPolicyNestedInput = {
    create?: XOR<PolicyDocumentCreateWithoutPolicyInput, PolicyDocumentUncheckedCreateWithoutPolicyInput> | PolicyDocumentCreateWithoutPolicyInput[] | PolicyDocumentUncheckedCreateWithoutPolicyInput[]
    connectOrCreate?: PolicyDocumentCreateOrConnectWithoutPolicyInput | PolicyDocumentCreateOrConnectWithoutPolicyInput[]
    upsert?: PolicyDocumentUpsertWithWhereUniqueWithoutPolicyInput | PolicyDocumentUpsertWithWhereUniqueWithoutPolicyInput[]
    createMany?: PolicyDocumentCreateManyPolicyInputEnvelope
    set?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
    disconnect?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
    delete?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
    connect?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
    update?: PolicyDocumentUpdateWithWhereUniqueWithoutPolicyInput | PolicyDocumentUpdateWithWhereUniqueWithoutPolicyInput[]
    updateMany?: PolicyDocumentUpdateManyWithWhereWithoutPolicyInput | PolicyDocumentUpdateManyWithWhereWithoutPolicyInput[]
    deleteMany?: PolicyDocumentScalarWhereInput | PolicyDocumentScalarWhereInput[]
  }

  export type PolicyDocumentUncheckedUpdateManyWithoutPolicyNestedInput = {
    create?: XOR<PolicyDocumentCreateWithoutPolicyInput, PolicyDocumentUncheckedCreateWithoutPolicyInput> | PolicyDocumentCreateWithoutPolicyInput[] | PolicyDocumentUncheckedCreateWithoutPolicyInput[]
    connectOrCreate?: PolicyDocumentCreateOrConnectWithoutPolicyInput | PolicyDocumentCreateOrConnectWithoutPolicyInput[]
    upsert?: PolicyDocumentUpsertWithWhereUniqueWithoutPolicyInput | PolicyDocumentUpsertWithWhereUniqueWithoutPolicyInput[]
    createMany?: PolicyDocumentCreateManyPolicyInputEnvelope
    set?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
    disconnect?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
    delete?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
    connect?: PolicyDocumentWhereUniqueInput | PolicyDocumentWhereUniqueInput[]
    update?: PolicyDocumentUpdateWithWhereUniqueWithoutPolicyInput | PolicyDocumentUpdateWithWhereUniqueWithoutPolicyInput[]
    updateMany?: PolicyDocumentUpdateManyWithWhereWithoutPolicyInput | PolicyDocumentUpdateManyWithWhereWithoutPolicyInput[]
    deleteMany?: PolicyDocumentScalarWhereInput | PolicyDocumentScalarWhereInput[]
  }

  export type PolicyCreateNestedOneWithoutDocumentsInput = {
    create?: XOR<PolicyCreateWithoutDocumentsInput, PolicyUncheckedCreateWithoutDocumentsInput>
    connectOrCreate?: PolicyCreateOrConnectWithoutDocumentsInput
    connect?: PolicyWhereUniqueInput
  }

  export type PolicyUpdateOneRequiredWithoutDocumentsNestedInput = {
    create?: XOR<PolicyCreateWithoutDocumentsInput, PolicyUncheckedCreateWithoutDocumentsInput>
    connectOrCreate?: PolicyCreateOrConnectWithoutDocumentsInput
    upsert?: PolicyUpsertWithoutDocumentsInput
    connect?: PolicyWhereUniqueInput
    update?: XOR<XOR<PolicyUpdateToOneWithWhereWithoutDocumentsInput, PolicyUpdateWithoutDocumentsInput>, PolicyUncheckedUpdateWithoutDocumentsInput>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type ClaimCreateWithoutEmployeeInput = {
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
    documents?: ClaimDocumentCreateNestedManyWithoutClaimInput
    comments?: ClaimCommentCreateNestedManyWithoutClaimInput
  }

  export type ClaimUncheckedCreateWithoutEmployeeInput = {
    id?: number
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
    documents?: ClaimDocumentUncheckedCreateNestedManyWithoutClaimInput
    comments?: ClaimCommentUncheckedCreateNestedManyWithoutClaimInput
  }

  export type ClaimCreateOrConnectWithoutEmployeeInput = {
    where: ClaimWhereUniqueInput
    create: XOR<ClaimCreateWithoutEmployeeInput, ClaimUncheckedCreateWithoutEmployeeInput>
  }

  export type ClaimCreateManyEmployeeInputEnvelope = {
    data: ClaimCreateManyEmployeeInput | ClaimCreateManyEmployeeInput[]
  }

  export type PolicyCreateWithoutEmployeeInput = {
    policyOwner: string
    insured: string
    spouse?: string | null
    group: string
    policyNumber: string
    originalEffectiveDate?: Date | string | null
    scheduledEffectiveDate?: Date | string | null
    issuedAge?: number | null
    insuredCoverage?: number | null
    spouseCoverage?: number | null
    documents?: PolicyDocumentCreateNestedManyWithoutPolicyInput
  }

  export type PolicyUncheckedCreateWithoutEmployeeInput = {
    id?: number
    policyOwner: string
    insured: string
    spouse?: string | null
    group: string
    policyNumber: string
    originalEffectiveDate?: Date | string | null
    scheduledEffectiveDate?: Date | string | null
    issuedAge?: number | null
    insuredCoverage?: number | null
    spouseCoverage?: number | null
    documents?: PolicyDocumentUncheckedCreateNestedManyWithoutPolicyInput
  }

  export type PolicyCreateOrConnectWithoutEmployeeInput = {
    where: PolicyWhereUniqueInput
    create: XOR<PolicyCreateWithoutEmployeeInput, PolicyUncheckedCreateWithoutEmployeeInput>
  }

  export type ClaimUpsertWithWhereUniqueWithoutEmployeeInput = {
    where: ClaimWhereUniqueInput
    update: XOR<ClaimUpdateWithoutEmployeeInput, ClaimUncheckedUpdateWithoutEmployeeInput>
    create: XOR<ClaimCreateWithoutEmployeeInput, ClaimUncheckedCreateWithoutEmployeeInput>
  }

  export type ClaimUpdateWithWhereUniqueWithoutEmployeeInput = {
    where: ClaimWhereUniqueInput
    data: XOR<ClaimUpdateWithoutEmployeeInput, ClaimUncheckedUpdateWithoutEmployeeInput>
  }

  export type ClaimUpdateManyWithWhereWithoutEmployeeInput = {
    where: ClaimScalarWhereInput
    data: XOR<ClaimUpdateManyMutationInput, ClaimUncheckedUpdateManyWithoutEmployeeInput>
  }

  export type ClaimScalarWhereInput = {
    AND?: ClaimScalarWhereInput | ClaimScalarWhereInput[]
    OR?: ClaimScalarWhereInput[]
    NOT?: ClaimScalarWhereInput | ClaimScalarWhereInput[]
    id?: IntFilter<"Claim"> | number
    employeeId?: IntFilter<"Claim"> | number
    claimType?: StringFilter<"Claim"> | string
    description?: StringFilter<"Claim"> | string
    incidentDate?: DateTimeNullableFilter<"Claim"> | Date | string | null
    dateFiled?: DateTimeFilter<"Claim"> | Date | string
    status?: StringFilter<"Claim"> | string
  }

  export type PolicyUpsertWithoutEmployeeInput = {
    update: XOR<PolicyUpdateWithoutEmployeeInput, PolicyUncheckedUpdateWithoutEmployeeInput>
    create: XOR<PolicyCreateWithoutEmployeeInput, PolicyUncheckedCreateWithoutEmployeeInput>
    where?: PolicyWhereInput
  }

  export type PolicyUpdateToOneWithWhereWithoutEmployeeInput = {
    where?: PolicyWhereInput
    data: XOR<PolicyUpdateWithoutEmployeeInput, PolicyUncheckedUpdateWithoutEmployeeInput>
  }

  export type PolicyUpdateWithoutEmployeeInput = {
    policyOwner?: StringFieldUpdateOperationsInput | string
    insured?: StringFieldUpdateOperationsInput | string
    spouse?: NullableStringFieldUpdateOperationsInput | string | null
    group?: StringFieldUpdateOperationsInput | string
    policyNumber?: StringFieldUpdateOperationsInput | string
    originalEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scheduledEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    issuedAge?: NullableIntFieldUpdateOperationsInput | number | null
    insuredCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    spouseCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    documents?: PolicyDocumentUpdateManyWithoutPolicyNestedInput
  }

  export type PolicyUncheckedUpdateWithoutEmployeeInput = {
    id?: IntFieldUpdateOperationsInput | number
    policyOwner?: StringFieldUpdateOperationsInput | string
    insured?: StringFieldUpdateOperationsInput | string
    spouse?: NullableStringFieldUpdateOperationsInput | string | null
    group?: StringFieldUpdateOperationsInput | string
    policyNumber?: StringFieldUpdateOperationsInput | string
    originalEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scheduledEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    issuedAge?: NullableIntFieldUpdateOperationsInput | number | null
    insuredCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    spouseCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    documents?: PolicyDocumentUncheckedUpdateManyWithoutPolicyNestedInput
  }

  export type EmployeeCreateWithoutClaimsInput = {
    firstName: string
    lastName: string
    email: string
    phone?: string | null
    address?: string | null
    employerName?: string | null
    groupId?: string | null
    memberId?: string | null
    policy?: PolicyCreateNestedOneWithoutEmployeeInput
  }

  export type EmployeeUncheckedCreateWithoutClaimsInput = {
    id?: number
    firstName: string
    lastName: string
    email: string
    phone?: string | null
    address?: string | null
    employerName?: string | null
    groupId?: string | null
    memberId?: string | null
    policy?: PolicyUncheckedCreateNestedOneWithoutEmployeeInput
  }

  export type EmployeeCreateOrConnectWithoutClaimsInput = {
    where: EmployeeWhereUniqueInput
    create: XOR<EmployeeCreateWithoutClaimsInput, EmployeeUncheckedCreateWithoutClaimsInput>
  }

  export type ClaimDocumentCreateWithoutClaimInput = {
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type ClaimDocumentUncheckedCreateWithoutClaimInput = {
    id?: number
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type ClaimDocumentCreateOrConnectWithoutClaimInput = {
    where: ClaimDocumentWhereUniqueInput
    create: XOR<ClaimDocumentCreateWithoutClaimInput, ClaimDocumentUncheckedCreateWithoutClaimInput>
  }

  export type ClaimDocumentCreateManyClaimInputEnvelope = {
    data: ClaimDocumentCreateManyClaimInput | ClaimDocumentCreateManyClaimInput[]
  }

  export type ClaimCommentCreateWithoutClaimInput = {
    text: string
    createdAt?: Date | string
  }

  export type ClaimCommentUncheckedCreateWithoutClaimInput = {
    id?: number
    text: string
    createdAt?: Date | string
  }

  export type ClaimCommentCreateOrConnectWithoutClaimInput = {
    where: ClaimCommentWhereUniqueInput
    create: XOR<ClaimCommentCreateWithoutClaimInput, ClaimCommentUncheckedCreateWithoutClaimInput>
  }

  export type ClaimCommentCreateManyClaimInputEnvelope = {
    data: ClaimCommentCreateManyClaimInput | ClaimCommentCreateManyClaimInput[]
  }

  export type EmployeeUpsertWithoutClaimsInput = {
    update: XOR<EmployeeUpdateWithoutClaimsInput, EmployeeUncheckedUpdateWithoutClaimsInput>
    create: XOR<EmployeeCreateWithoutClaimsInput, EmployeeUncheckedCreateWithoutClaimsInput>
    where?: EmployeeWhereInput
  }

  export type EmployeeUpdateToOneWithWhereWithoutClaimsInput = {
    where?: EmployeeWhereInput
    data: XOR<EmployeeUpdateWithoutClaimsInput, EmployeeUncheckedUpdateWithoutClaimsInput>
  }

  export type EmployeeUpdateWithoutClaimsInput = {
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    employerName?: NullableStringFieldUpdateOperationsInput | string | null
    groupId?: NullableStringFieldUpdateOperationsInput | string | null
    memberId?: NullableStringFieldUpdateOperationsInput | string | null
    policy?: PolicyUpdateOneWithoutEmployeeNestedInput
  }

  export type EmployeeUncheckedUpdateWithoutClaimsInput = {
    id?: IntFieldUpdateOperationsInput | number
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    employerName?: NullableStringFieldUpdateOperationsInput | string | null
    groupId?: NullableStringFieldUpdateOperationsInput | string | null
    memberId?: NullableStringFieldUpdateOperationsInput | string | null
    policy?: PolicyUncheckedUpdateOneWithoutEmployeeNestedInput
  }

  export type ClaimDocumentUpsertWithWhereUniqueWithoutClaimInput = {
    where: ClaimDocumentWhereUniqueInput
    update: XOR<ClaimDocumentUpdateWithoutClaimInput, ClaimDocumentUncheckedUpdateWithoutClaimInput>
    create: XOR<ClaimDocumentCreateWithoutClaimInput, ClaimDocumentUncheckedCreateWithoutClaimInput>
  }

  export type ClaimDocumentUpdateWithWhereUniqueWithoutClaimInput = {
    where: ClaimDocumentWhereUniqueInput
    data: XOR<ClaimDocumentUpdateWithoutClaimInput, ClaimDocumentUncheckedUpdateWithoutClaimInput>
  }

  export type ClaimDocumentUpdateManyWithWhereWithoutClaimInput = {
    where: ClaimDocumentScalarWhereInput
    data: XOR<ClaimDocumentUpdateManyMutationInput, ClaimDocumentUncheckedUpdateManyWithoutClaimInput>
  }

  export type ClaimDocumentScalarWhereInput = {
    AND?: ClaimDocumentScalarWhereInput | ClaimDocumentScalarWhereInput[]
    OR?: ClaimDocumentScalarWhereInput[]
    NOT?: ClaimDocumentScalarWhereInput | ClaimDocumentScalarWhereInput[]
    id?: IntFilter<"ClaimDocument"> | number
    claimId?: IntFilter<"ClaimDocument"> | number
    fileName?: StringFilter<"ClaimDocument"> | string
    filePath?: StringFilter<"ClaimDocument"> | string
    uploadedAt?: DateTimeFilter<"ClaimDocument"> | Date | string
  }

  export type ClaimCommentUpsertWithWhereUniqueWithoutClaimInput = {
    where: ClaimCommentWhereUniqueInput
    update: XOR<ClaimCommentUpdateWithoutClaimInput, ClaimCommentUncheckedUpdateWithoutClaimInput>
    create: XOR<ClaimCommentCreateWithoutClaimInput, ClaimCommentUncheckedCreateWithoutClaimInput>
  }

  export type ClaimCommentUpdateWithWhereUniqueWithoutClaimInput = {
    where: ClaimCommentWhereUniqueInput
    data: XOR<ClaimCommentUpdateWithoutClaimInput, ClaimCommentUncheckedUpdateWithoutClaimInput>
  }

  export type ClaimCommentUpdateManyWithWhereWithoutClaimInput = {
    where: ClaimCommentScalarWhereInput
    data: XOR<ClaimCommentUpdateManyMutationInput, ClaimCommentUncheckedUpdateManyWithoutClaimInput>
  }

  export type ClaimCommentScalarWhereInput = {
    AND?: ClaimCommentScalarWhereInput | ClaimCommentScalarWhereInput[]
    OR?: ClaimCommentScalarWhereInput[]
    NOT?: ClaimCommentScalarWhereInput | ClaimCommentScalarWhereInput[]
    id?: IntFilter<"ClaimComment"> | number
    claimId?: IntFilter<"ClaimComment"> | number
    text?: StringFilter<"ClaimComment"> | string
    createdAt?: DateTimeFilter<"ClaimComment"> | Date | string
  }

  export type ClaimCreateWithoutDocumentsInput = {
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
    employee: EmployeeCreateNestedOneWithoutClaimsInput
    comments?: ClaimCommentCreateNestedManyWithoutClaimInput
  }

  export type ClaimUncheckedCreateWithoutDocumentsInput = {
    id?: number
    employeeId: number
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
    comments?: ClaimCommentUncheckedCreateNestedManyWithoutClaimInput
  }

  export type ClaimCreateOrConnectWithoutDocumentsInput = {
    where: ClaimWhereUniqueInput
    create: XOR<ClaimCreateWithoutDocumentsInput, ClaimUncheckedCreateWithoutDocumentsInput>
  }

  export type ClaimUpsertWithoutDocumentsInput = {
    update: XOR<ClaimUpdateWithoutDocumentsInput, ClaimUncheckedUpdateWithoutDocumentsInput>
    create: XOR<ClaimCreateWithoutDocumentsInput, ClaimUncheckedCreateWithoutDocumentsInput>
    where?: ClaimWhereInput
  }

  export type ClaimUpdateToOneWithWhereWithoutDocumentsInput = {
    where?: ClaimWhereInput
    data: XOR<ClaimUpdateWithoutDocumentsInput, ClaimUncheckedUpdateWithoutDocumentsInput>
  }

  export type ClaimUpdateWithoutDocumentsInput = {
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
    employee?: EmployeeUpdateOneRequiredWithoutClaimsNestedInput
    comments?: ClaimCommentUpdateManyWithoutClaimNestedInput
  }

  export type ClaimUncheckedUpdateWithoutDocumentsInput = {
    id?: IntFieldUpdateOperationsInput | number
    employeeId?: IntFieldUpdateOperationsInput | number
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
    comments?: ClaimCommentUncheckedUpdateManyWithoutClaimNestedInput
  }

  export type ClaimCreateWithoutCommentsInput = {
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
    employee: EmployeeCreateNestedOneWithoutClaimsInput
    documents?: ClaimDocumentCreateNestedManyWithoutClaimInput
  }

  export type ClaimUncheckedCreateWithoutCommentsInput = {
    id?: number
    employeeId: number
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
    documents?: ClaimDocumentUncheckedCreateNestedManyWithoutClaimInput
  }

  export type ClaimCreateOrConnectWithoutCommentsInput = {
    where: ClaimWhereUniqueInput
    create: XOR<ClaimCreateWithoutCommentsInput, ClaimUncheckedCreateWithoutCommentsInput>
  }

  export type ClaimUpsertWithoutCommentsInput = {
    update: XOR<ClaimUpdateWithoutCommentsInput, ClaimUncheckedUpdateWithoutCommentsInput>
    create: XOR<ClaimCreateWithoutCommentsInput, ClaimUncheckedCreateWithoutCommentsInput>
    where?: ClaimWhereInput
  }

  export type ClaimUpdateToOneWithWhereWithoutCommentsInput = {
    where?: ClaimWhereInput
    data: XOR<ClaimUpdateWithoutCommentsInput, ClaimUncheckedUpdateWithoutCommentsInput>
  }

  export type ClaimUpdateWithoutCommentsInput = {
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
    employee?: EmployeeUpdateOneRequiredWithoutClaimsNestedInput
    documents?: ClaimDocumentUpdateManyWithoutClaimNestedInput
  }

  export type ClaimUncheckedUpdateWithoutCommentsInput = {
    id?: IntFieldUpdateOperationsInput | number
    employeeId?: IntFieldUpdateOperationsInput | number
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
    documents?: ClaimDocumentUncheckedUpdateManyWithoutClaimNestedInput
  }

  export type EmployeeCreateWithoutPolicyInput = {
    firstName: string
    lastName: string
    email: string
    phone?: string | null
    address?: string | null
    employerName?: string | null
    groupId?: string | null
    memberId?: string | null
    claims?: ClaimCreateNestedManyWithoutEmployeeInput
  }

  export type EmployeeUncheckedCreateWithoutPolicyInput = {
    id?: number
    firstName: string
    lastName: string
    email: string
    phone?: string | null
    address?: string | null
    employerName?: string | null
    groupId?: string | null
    memberId?: string | null
    claims?: ClaimUncheckedCreateNestedManyWithoutEmployeeInput
  }

  export type EmployeeCreateOrConnectWithoutPolicyInput = {
    where: EmployeeWhereUniqueInput
    create: XOR<EmployeeCreateWithoutPolicyInput, EmployeeUncheckedCreateWithoutPolicyInput>
  }

  export type PolicyDocumentCreateWithoutPolicyInput = {
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type PolicyDocumentUncheckedCreateWithoutPolicyInput = {
    id?: number
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type PolicyDocumentCreateOrConnectWithoutPolicyInput = {
    where: PolicyDocumentWhereUniqueInput
    create: XOR<PolicyDocumentCreateWithoutPolicyInput, PolicyDocumentUncheckedCreateWithoutPolicyInput>
  }

  export type PolicyDocumentCreateManyPolicyInputEnvelope = {
    data: PolicyDocumentCreateManyPolicyInput | PolicyDocumentCreateManyPolicyInput[]
  }

  export type EmployeeUpsertWithoutPolicyInput = {
    update: XOR<EmployeeUpdateWithoutPolicyInput, EmployeeUncheckedUpdateWithoutPolicyInput>
    create: XOR<EmployeeCreateWithoutPolicyInput, EmployeeUncheckedCreateWithoutPolicyInput>
    where?: EmployeeWhereInput
  }

  export type EmployeeUpdateToOneWithWhereWithoutPolicyInput = {
    where?: EmployeeWhereInput
    data: XOR<EmployeeUpdateWithoutPolicyInput, EmployeeUncheckedUpdateWithoutPolicyInput>
  }

  export type EmployeeUpdateWithoutPolicyInput = {
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    employerName?: NullableStringFieldUpdateOperationsInput | string | null
    groupId?: NullableStringFieldUpdateOperationsInput | string | null
    memberId?: NullableStringFieldUpdateOperationsInput | string | null
    claims?: ClaimUpdateManyWithoutEmployeeNestedInput
  }

  export type EmployeeUncheckedUpdateWithoutPolicyInput = {
    id?: IntFieldUpdateOperationsInput | number
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    employerName?: NullableStringFieldUpdateOperationsInput | string | null
    groupId?: NullableStringFieldUpdateOperationsInput | string | null
    memberId?: NullableStringFieldUpdateOperationsInput | string | null
    claims?: ClaimUncheckedUpdateManyWithoutEmployeeNestedInput
  }

  export type PolicyDocumentUpsertWithWhereUniqueWithoutPolicyInput = {
    where: PolicyDocumentWhereUniqueInput
    update: XOR<PolicyDocumentUpdateWithoutPolicyInput, PolicyDocumentUncheckedUpdateWithoutPolicyInput>
    create: XOR<PolicyDocumentCreateWithoutPolicyInput, PolicyDocumentUncheckedCreateWithoutPolicyInput>
  }

  export type PolicyDocumentUpdateWithWhereUniqueWithoutPolicyInput = {
    where: PolicyDocumentWhereUniqueInput
    data: XOR<PolicyDocumentUpdateWithoutPolicyInput, PolicyDocumentUncheckedUpdateWithoutPolicyInput>
  }

  export type PolicyDocumentUpdateManyWithWhereWithoutPolicyInput = {
    where: PolicyDocumentScalarWhereInput
    data: XOR<PolicyDocumentUpdateManyMutationInput, PolicyDocumentUncheckedUpdateManyWithoutPolicyInput>
  }

  export type PolicyDocumentScalarWhereInput = {
    AND?: PolicyDocumentScalarWhereInput | PolicyDocumentScalarWhereInput[]
    OR?: PolicyDocumentScalarWhereInput[]
    NOT?: PolicyDocumentScalarWhereInput | PolicyDocumentScalarWhereInput[]
    id?: IntFilter<"PolicyDocument"> | number
    policyId?: IntFilter<"PolicyDocument"> | number
    fileName?: StringFilter<"PolicyDocument"> | string
    filePath?: StringFilter<"PolicyDocument"> | string
    uploadedAt?: DateTimeFilter<"PolicyDocument"> | Date | string
  }

  export type PolicyCreateWithoutDocumentsInput = {
    policyOwner: string
    insured: string
    spouse?: string | null
    group: string
    policyNumber: string
    originalEffectiveDate?: Date | string | null
    scheduledEffectiveDate?: Date | string | null
    issuedAge?: number | null
    insuredCoverage?: number | null
    spouseCoverage?: number | null
    employee: EmployeeCreateNestedOneWithoutPolicyInput
  }

  export type PolicyUncheckedCreateWithoutDocumentsInput = {
    id?: number
    employeeId: number
    policyOwner: string
    insured: string
    spouse?: string | null
    group: string
    policyNumber: string
    originalEffectiveDate?: Date | string | null
    scheduledEffectiveDate?: Date | string | null
    issuedAge?: number | null
    insuredCoverage?: number | null
    spouseCoverage?: number | null
  }

  export type PolicyCreateOrConnectWithoutDocumentsInput = {
    where: PolicyWhereUniqueInput
    create: XOR<PolicyCreateWithoutDocumentsInput, PolicyUncheckedCreateWithoutDocumentsInput>
  }

  export type PolicyUpsertWithoutDocumentsInput = {
    update: XOR<PolicyUpdateWithoutDocumentsInput, PolicyUncheckedUpdateWithoutDocumentsInput>
    create: XOR<PolicyCreateWithoutDocumentsInput, PolicyUncheckedCreateWithoutDocumentsInput>
    where?: PolicyWhereInput
  }

  export type PolicyUpdateToOneWithWhereWithoutDocumentsInput = {
    where?: PolicyWhereInput
    data: XOR<PolicyUpdateWithoutDocumentsInput, PolicyUncheckedUpdateWithoutDocumentsInput>
  }

  export type PolicyUpdateWithoutDocumentsInput = {
    policyOwner?: StringFieldUpdateOperationsInput | string
    insured?: StringFieldUpdateOperationsInput | string
    spouse?: NullableStringFieldUpdateOperationsInput | string | null
    group?: StringFieldUpdateOperationsInput | string
    policyNumber?: StringFieldUpdateOperationsInput | string
    originalEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scheduledEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    issuedAge?: NullableIntFieldUpdateOperationsInput | number | null
    insuredCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    spouseCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    employee?: EmployeeUpdateOneRequiredWithoutPolicyNestedInput
  }

  export type PolicyUncheckedUpdateWithoutDocumentsInput = {
    id?: IntFieldUpdateOperationsInput | number
    employeeId?: IntFieldUpdateOperationsInput | number
    policyOwner?: StringFieldUpdateOperationsInput | string
    insured?: StringFieldUpdateOperationsInput | string
    spouse?: NullableStringFieldUpdateOperationsInput | string | null
    group?: StringFieldUpdateOperationsInput | string
    policyNumber?: StringFieldUpdateOperationsInput | string
    originalEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scheduledEffectiveDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    issuedAge?: NullableIntFieldUpdateOperationsInput | number | null
    insuredCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
    spouseCoverage?: NullableFloatFieldUpdateOperationsInput | number | null
  }

  export type ClaimCreateManyEmployeeInput = {
    id?: number
    claimType?: string
    description: string
    incidentDate?: Date | string | null
    dateFiled?: Date | string
    status: string
  }

  export type ClaimUpdateWithoutEmployeeInput = {
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
    documents?: ClaimDocumentUpdateManyWithoutClaimNestedInput
    comments?: ClaimCommentUpdateManyWithoutClaimNestedInput
  }

  export type ClaimUncheckedUpdateWithoutEmployeeInput = {
    id?: IntFieldUpdateOperationsInput | number
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
    documents?: ClaimDocumentUncheckedUpdateManyWithoutClaimNestedInput
    comments?: ClaimCommentUncheckedUpdateManyWithoutClaimNestedInput
  }

  export type ClaimUncheckedUpdateManyWithoutEmployeeInput = {
    id?: IntFieldUpdateOperationsInput | number
    claimType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    incidentDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    dateFiled?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: StringFieldUpdateOperationsInput | string
  }

  export type ClaimDocumentCreateManyClaimInput = {
    id?: number
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type ClaimCommentCreateManyClaimInput = {
    id?: number
    text: string
    createdAt?: Date | string
  }

  export type ClaimDocumentUpdateWithoutClaimInput = {
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimDocumentUncheckedUpdateWithoutClaimInput = {
    id?: IntFieldUpdateOperationsInput | number
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimDocumentUncheckedUpdateManyWithoutClaimInput = {
    id?: IntFieldUpdateOperationsInput | number
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimCommentUpdateWithoutClaimInput = {
    text?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimCommentUncheckedUpdateWithoutClaimInput = {
    id?: IntFieldUpdateOperationsInput | number
    text?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClaimCommentUncheckedUpdateManyWithoutClaimInput = {
    id?: IntFieldUpdateOperationsInput | number
    text?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PolicyDocumentCreateManyPolicyInput = {
    id?: number
    fileName: string
    filePath: string
    uploadedAt?: Date | string
  }

  export type PolicyDocumentUpdateWithoutPolicyInput = {
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PolicyDocumentUncheckedUpdateWithoutPolicyInput = {
    id?: IntFieldUpdateOperationsInput | number
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PolicyDocumentUncheckedUpdateManyWithoutPolicyInput = {
    id?: IntFieldUpdateOperationsInput | number
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}